import kong.unirest.Unirest

import java.text.SimpleDateFormat

buildscript {
    ext {
        kotlinVersion = '1.2.41'
        springBootVersion = '2.0.4.RELEASE'
    }
    repositories {
        maven {url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlinVersion}")
        classpath("org.jetbrains.kotlin:kotlin-allopen:${kotlinVersion}")
        classpath 'org.hidetake:gradle-ssh-plugin:2.9.0'
        classpath group: 'com.konghq', name: 'unirest-java', version: '3.11.11'
    }
}

apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'
apply plugin: 'org.hidetake.ssh'

group = 'com.shenlan.markmiss'
version = ''
sourceCompatibility = 1.8
compileKotlin {
    kotlinOptions {
        freeCompilerArgs = ["-Xjsr305=strict"]
        jvmTarget = "1.8"
    }
}
compileTestKotlin {
    kotlinOptions {
        freeCompilerArgs = ["-Xjsr305=strict"]
        jvmTarget = "1.8"
    }
}
springBoot {
    mainClassName  = "com.shenlan.cnais.ApplicationKt"
}
ssh.settings {
    knownHosts = allowAnyHosts
}

remotes {
    smsserver_1 {
        host = '************'
        port = 22
        user = "root"
        password = "SHENLAN@2016"
    }
}

task upload(dependsOn: war) doLast {
    ssh.run {
        session(remotes.smsserver_1) {
            put from: 'build/libs/cnaisdata.war', into: '/shenlan/tomcat/cnaisdata/cnaisdata.war'
            execute 'sh /shenlan/tomcat/cnaisdata/deploy_tomcat.sh'
        }
    }
}
task upload2Prod() doLast{
    Unirest.post("http://************:11201/api/maintain/deploy").field("file",file("build/libs/datacenterdata.war")).asEmpty()
}
//task ci(){//打包时 将打包时间写入ci.txt
//    File file = new File('src/main/resources/ci.txt')
//
//    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm")
//    println sdf.format(new Date())
//    file.write(sdf.format(new Date()))
//}
//clean.dependsOn ci

repositories {
    maven {url 'http://maven.aliyun.com/nexus/content/groups/public/'}
    mavenCentral()
    jcenter()
}

test.enabled = false//取消test

war{//根目录设置为build/www
    webAppDirName="www"
}

def profile1 = project.hasProperty("profile") == true ? profile : "dev"
processResources {
    filter org.apache.tools.ant.filters.ReplaceTokens, tokens: [profileName: profile1]
}

dependencies {
//    compile fileTree(dir: 'lib', include: ['*.jar'])
    compile fileTree(dir: 'lib', include: ['fastdfs-client-java-1.27-SNAPSHOT.jar'])
    compile('org.springframework.boot:spring-boot-starter-web')
    compile('org.springframework.boot:spring-boot-starter-aop')
    compile('com.fasterxml.jackson.module:jackson-module-kotlin')
    compile('mysql:mysql-connector-java')
    compile('org.springframework.boot:spring-boot-starter-websocket')
//    compile('org.springframework.session:spring-session-jdbc')
    compile('org.mybatis.spring.boot:mybatis-spring-boot-starter:1.3.2')
    compile('org.springframework.boot:spring-boot-starter-security')
    compile("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    compile("org.jetbrains.kotlin:kotlin-reflect")
    compile('com.alibaba:fastjson:1.2.49')
    compile group: 'com.aliyun', name: 'aliyun-java-sdk-core', version: '4.0.3'
    runtime('org.springframework.boot:spring-boot-devtools')
    compile 'commons-beanutils:commons-beanutils:1.9.3'
    compile 'io.reactivex.rxjava3:rxjava:3.0.4'
    compile("org.springframework.boot:spring-boot-starter-activemq")
    compile("org.apache.activemq:activemq-broker")
    implementation 'org.reflections:reflections:0.9.11'
    implementation 'dk.dma.enav:enav-model:0.6'
    implementation 'dk.dma.commons:dma-commons-util:0.5'
    implementation 'org.antlr:antlr4-runtime:4.9.2'
    compile 'ru.yandex.clickhouse:clickhouse-jdbc:0.3.1'
    compile group: 'org.apache.mina', name: 'mina-core', version:'2.0.7'
    compile 'ch.hsr:geohash:1.4.0'
    compile group: 'com.konghq', name: 'unirest-java', version: '3.11.11'
//    compile group: 'com.github.jsonzou', name: 'jmockdata', version: '4.1.2'
//    compile("org.springframework.boot:spring-boot-starter-activemq")
//    compile("org.apache.activemq:activemq-broker")
//    compile group: 'io.netty', name: 'netty-all', version: '4.1.41.Final'
//    compile group: 'net.coobird', name: 'thumbnailator', version: '0.4.8'
//    compile "com.microsoft.sqlserver:mssql-jdbc:6.1.0.jre8"
//    compile group: 'org.apache.mina', name: 'mina-core', version:'2.1.3'
    compile group: 'commons-io', name: 'commons-io', version: '2.6'
    implementation 'org.apache.commons:commons-collections4:4.4'
//    compile group: 'com.github.tobato', name: 'fastdfs-client', version: '1.26.6'
//    compile group: 'org.csource', name: 'fastdfs-client-java', version: '1.27-RELEASE'
    testCompile('org.springframework.boot:spring-boot-starter-test')
    implementation 'org.yaml:snakeyaml:1.19'

//    implementation 'org.apache.kafka:kafka_2.11:1.0.2'
//    implementation 'org.apache.kafka:kafka-streams:1.0.2'
    implementation 'org.apache.kafka:kafka-clients:1.0.2'
    implementation 'org.springframework.kafka:spring-kafka:2.1.4.RELEASE'

    implementation("org.postgresql:postgresql:42.6.0")
    implementation("com.vividsolutions:jts:1.13")
    implementation("khttp:khttp:1.0.0")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.bouncycastle:bcprov-jdk15to18:1.71")
}
