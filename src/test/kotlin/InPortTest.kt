import com.shenlan.cnais.util.dateTimeFormatter
import com.shenlan.cnais.util.jacksonObjectMapper
import com.shenlan.cnais.util.println
import com.shenlan.cnais.util.toLocalDateTime
import org.bouncycastle.jce.provider.BouncyCastleProvider
import java.security.Security
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec
import org.apache.commons.codec.binary.Base64
import org.junit.Test
import java.security.MessageDigest
import java.time.LocalDateTime

class InPortTest {
    fun decrypt(content: String, key: String): String? {
        return try {
            val base64Bytes = Base64.decodeBase64(content)
            val secretKeySpec = SecretKeySpec(key.toByteArray(), "AES")
            Security.addProvider(BouncyCastleProvider())
            val cipher = Cipher.getInstance("AES/ECB/PKCS7Padding", "BC")
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec)
            val decryptedBytes = cipher.doFinal(base64Bytes)
            String(decryptedBytes)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    data class Response(
        var code: Int,
        var msg: String,
        var data: String
    )

    @Test
    fun test1() {
        val password = "MSA@2025Fish"
        val md5 = MessageDigest.getInstance("MD5")
        val hash = md5.digest(password.toByteArray()).joinToString("") { "%02x".format(it) }.toUpperCase()
        val time = dateTimeFormatter.format(LocalDateTime.now())
        println(md5.digest("$hash$time".toByteArray()).joinToString("") { "%02x".format(it) }.toUpperCase())
        println(time)
    }
}