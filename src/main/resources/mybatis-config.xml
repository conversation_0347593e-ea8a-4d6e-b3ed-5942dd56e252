<?mapper version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <environments default="dev">
        <environment id="dev">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.jdbc.Driver"/>
                <property name="url" value="***************************************************************************************************************************"/>
                <property name="username" value="root"/>
                <property name="password" value="SHENLAN@2016"/>
                <property name="poolMaximumActiveConnections" value="30"/>
                <property name="poolMaximumIdleConnections" value="10"/>
            </dataSource>
        </environment>
        <environment id="prod">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.jdbc.Driver"/>
                <property name="url" value="*****************************************"/>
                <property name="username" value="fis"/>
                <property name="password" value="fis"/>
                <property name="poolMaximumActiveConnections" value="30"/>
                <property name="poolMaximumIdleConnections" value="10"/>
            </dataSource>
        </environment>
        <environment id="proddev">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="oracle.jdbc.driver.OracleDriver"/>
                <property name="url" value="*****************************************"/>
                <property name="username" value="fis"/>
                <property name="password" value="fis"/>
                <property name="poolMaximumActiveConnections" value="30"/>
                <property name="poolMaximumIdleConnections" value="10"/>
            </dataSource>
        </environment>
    </environments>
    <mappers>
        <package name="com.shenlan"/>
    </mappers>
</configuration>