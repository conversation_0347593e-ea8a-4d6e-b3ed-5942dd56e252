import com.fasterxml.jackson.module.kotlin.readValue
import com.shenlan.cnais.ais.WebClientResponse
import com.shenlan.cnais.ais.client.WebClientUtil.getDataList
import com.shenlan.cnais.ais.client.WebClientUtil.responseClassesMap
import com.shenlan.cnais.ais.client.model.GuangdongResponse
import com.shenlan.cnais.ais.client.model.GuangxiResponse
import com.shenlan.cnais.ais.client.model.HainanResponse
import com.shenlan.cnais.ais.client.model.HainanResult
import com.shenlan.cnais.ais.client.model.ZhejiangResponse
import com.shenlan.cnais.ais.server.beidou.BDShipLoginRequest
import com.shenlan.cnais.ais.server.beidou.BDShipUtil.copyWithRelatedProperties
import com.shenlan.cnais.util.*
import com.shenlan.cnais.util.Sm4CbcUtil.hexToByteArray
import com.shenlan.dateFormat
import org.junit.Test
import util.Sm4Util
import java.io.File
import java.io.FileWriter
import java.security.MessageDigest
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Base64
import java.util.Date

class WebClientTest {
    data class Person(val name: String, val age: Int)

    @Test
    fun test1() {
        val plainText = "shipReg/hnFishShipAll?startTime=2025-05-30%2000:08:43&endTime=2025-05-30%2018:13:43#${localDateFormatter.format(LocalDate.now())}#a90542f8bad34aebb03645246f54c96e"
        println(plainText)
        println(Sm4Util.encryptEcb("c1e3ff7bfb3f4afebdacad941bf00bc7", plainText))
        println(dateTimeFormatter.format(LocalDate.now().atStartOfDay()))
//        Md5Util.encrypt()
        val str = "{\"DRAUGHT\":\"\",\"LNG\":\"109.912502\",\"STATE\":\"\",\"BM\":\"\",\"HEADING\":\"\",\"MMSI\":\"\",\"EPFS\":\"\",\"COG\":\"0.0\",\"ID\":\"BD\",\"SN\":\"\",\"TYPE\":\"30\",\"LOA\":\"\",\"TIMESTIMP\":\"\",\"FROMTYPE\":\"14\",\"RCVTIME\":\"2025-05-21 16:59:12\",\"MID\":\"\",\"IMO\":\"\",\"UMC\":\"\",\"SOG\":\"1\",\"NAME\":\"\",\"CALLSIGN\":\"\",\"ETA\":\"\",\"ROT\":\"\",\"DEST\":\"\",\"LAT\":\"19.98470233\"}"
        println(jacksonObjectMapperIgnoreCase.readValue(str, HainanResult::class.java).toJsonString)
        val bdShipData = copyWithRelatedProperties(jacksonObjectMapperIgnoreCase.readValue(str, HainanResult::class.java))
        println(bdShipData.toJsonString)
//        File("E:\\workspace\\hainanres").bufferedReader().forEachLine { data ->
//            if (data.isNotEmpty()) {
//                val list = jacksonObjectMapperIgnoreCase.readValue(data, HainanResponse::class.java).result
//                println(list.size)
//            }
//        }

    }

    /**
     * 将 ByteArray 转换为十六进制字符串
     */
    fun ByteArray.toHexString(): String =
        joinToString("") { byte -> "%02x".format(byte) }

    fun deserializeResponse(uniqueId: String, json: String): List<*> {
        // 1. 根据 uniqueId 获取对应的 Class
//        val responseClass = responseClassesMap[uniqueId]
//            ?: throw IllegalArgumentException("No class found for uniqueId: $uniqueId")
        val responseClass = Person::class.java

        // 2. 创建 ObjectMapper

        // 3. 反序列化为单个对象
        // return mapper.readValue(json, responseClass)

        // 或者如果是列表（根据你的示例）
        val listType = jacksonObjectMapper.typeFactory.constructCollectionType(List::class.java, responseClass)
        return jacksonObjectMapper.readValue(json, listType)
    }


}