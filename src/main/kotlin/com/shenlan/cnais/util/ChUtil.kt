package com.shenlan.cnais.util

import com.shenlan.BaseModel
import com.shenlan.cnais.ais.ChModel
import com.shenlan.cnais.ais.Chdatagram
import com.shenlan.cnais.ais.Chposition
import com.shenlan.cnais.auto.*
import com.shenlan.cnais.config.AppPro
import com.shenlan.cnais.config.OpenMinaServer
import org.springframework.util.StopWatch
import ru.yandex.clickhouse.ClickHouseConnection
import ru.yandex.clickhouse.ClickHouseDataSource
import java.sql.ResultSet
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread
import kotlin.reflect.full.memberProperties

/**
 * Created by Administrator on 2021/12/3.
 */

object ChUtil {
    var aisShipMap = mutableMapOf<Int, String>()
    var modelListMap = mutableMapOf<String, MutableMap<String, BaseModel>>(Aisposition::class.java.simpleName to mutableMapOf(),
        Aisvirtualaton::class.java.simpleName to mutableMapOf(), Aisship::class.java.simpleName to mutableMapOf(), Aispositionhede::class.java.simpleName to mutableMapOf())
    var lastUpdateTimeMap = mutableMapOf<String, Long>(Aisposition::class.java.simpleName to System.currentTimeMillis(), Aisvirtualaton::class.java.simpleName to System.currentTimeMillis(),
        Aisship::class.java.simpleName to System.currentTimeMillis(), Aispositionhede::class.java.simpleName to System.currentTimeMillis())
    var executor: ThreadPoolExecutor = ThreadPoolExecutor(3, 100, 60L, TimeUnit.SECONDS, LinkedBlockingQueue())

    var conn: ClickHouseConnection? = null
        get() {
            if (field == null) {
                return ClickHouseDataSource(AppPro.chUrl).getConnection("default", "123456")
            } else {
                return field
            }
            return null
        }

    var errorTimeList = mutableListOf<Long>()

    fun insertToPosition(list: List<Chposition>) {
        if(AppPro.active=="dev") return
        if (list.isEmpty()) return

        val tableName = "ais_position_all"
        val ps = conn!!.prepareStatement("INSERT INTO default.${tableName} VALUES(?,?,?,?,?,?,?,?,?,?,?,?)")

        list.forEach {
            ps.setString(1, it.date)
            ps.setString(2, it.mmsi)
            ps.setInt(3, it.code)
            ps.setInt(4, it.turn)
            ps.setFloat(5, it.speed)
            ps.setInt(6, it.accuracy)
            ps.setString(7, it.geohash)
            ps.setFloat(8, it.lat)
            ps.setFloat(9, it.lng)
            ps.setFloat(10, it.course)
            ps.setInt(11, it.heading)
            ps.setString(12, it.fromType)
            ps.addBatch()
        }
        ps.executeBatch()
        ps.close() //        log.info("insertToPosition: ${list.size}")
    }

    fun insertList(list: List<Any>, table: String) {
        if (list.isEmpty()) return
        val columns = getSqlList("""
            DESCRIBE TABLE ${table};
        """.trimIndent()).map { it.getOrDefault("name", "") }

        val ps = conn!!.prepareStatement("""
            INSERT INTO default.${table} VALUES(${columns.joinToString(",") { "?" }});
        """.trimIndent())

        try {
            list.forEach { item ->
                val properties = item::class.memberProperties
                columns.forEachIndexed { index, column ->
                    properties.find { it.name == column }?.let { property ->
                        val value = property.getter.call(item)
                        if (value != null) {
                            when (value) {
                                is String -> ps.setString(index + 1, value)
                                is Int -> ps.setInt(index + 1, value)
                                is Float -> ps.setFloat(index + 1, value)
                                is Double -> ps.setFloat(index + 1, value.toFloat())
                                is Long -> ps.setLong(index + 1, value)
                                is Date -> ps.setString(index + 1, SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(value))
                                else -> ps.setString(index + 1, value.toString())
                            }
                        } else {
                            throw Exception("Column $column is null")
                        }
                    }
                }
                ps.addBatch()
            }
        } catch (e: Exception) {
            log.error(e.message)
        }

        ps.executeBatch()
        ps.close()
    }

    fun insertChmodel(chModel: ChModel) {
        val a = System.currentTimeMillis()
        var b = 0L
        var c = 0L
        try {
            insertList(chModel.bdPositionList, "ais_beidouposition_all")
            insertList(chModel.bdShipInfoList, "ais_beidouship_all")
//            insertToPosition(chModel.aispositionList)
            b = System.currentTimeMillis()
        } catch (e: Throwable) {
            log.error(e.message)
        }
        try {
            dealPosition(chModel)
            updateDb(chModel)
            c = System.currentTimeMillis()
        } catch (e: Throwable) {
            errorlog(e)
            log.error(e.message)
        }
        log.info("ch beidou position size: ${chModel.bdPositionList.size}, beidou ship size: ${chModel.bdShipInfoList.size},chtime:${b - a}, db time:${c - b}")
    }

    fun dealPosition(chModel: ChModel) {
        chModel.aispositionList.forEach {
            // 和德卫星数据另存一张表
            try {
                chModel.aispositionList2.add(Aisposition().apply {
                    id = it.mmsi
                    mmsi = it.mmsi
                    navigationStatusCode = it.code
                    rateOfTurn = it.turn
                    speedOverGround = it.speed
                    positionAccuracy = it.accuracy
                    longitude = it.lng
                    latitude = it.lat
                    courseOverGround = it.course
                    heading = it.heading
                    reportTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(it.date)
                    fromType = it.fromType
                })
            } catch (e: Exception) {
                log.error(e.message)
            }
        }
    }

    fun updateDb(chModel: ChModel) {
//        val aisPositionList = chModel.aispositionList2
//        Thread {
//            log.info("sessionList size: ${OpenMinaServer.aisPositionMinaModel.getSessionList()?.size}, aisPositionList size: ${aisPositionList.size}")
//            OpenMinaServer.aisPositionMinaModel.getSessionList()?.forEach { session ->
//                aisPositionList.forEach {
//                    // 所有数据源的动态信息都在这里汇集，更新缓存
//                    session.write(it.toJsonString)
//                }
//            }
//            aisPositionList.clear()
//        }.start()
    }

    fun getSqlList(sql: String): MutableList<HashMap<String, Any>> {
//        log.info(sql)
        val stopwatch = StopWatch()
        stopwatch.start("executeSql")
        val rs = conn!!.createStatement().executeQuery(sql)
        stopwatch.stop()
        stopwatch.start("convertList")
        val list = convertList(rs)
        stopwatch.stop()
        rs.close()
//        log.info(stopwatch.prettyPrint() + "size: ${list.size}")
        return list
    }

    fun convertList(rs: ResultSet): MutableList<HashMap<String, Any>> {
        val list = mutableListOf<HashMap<String, Any>>()
        val md = rs.metaData
        val columnCount = md.columnCount
        while (rs.next()) {
            val rowData = hashMapOf<String, Any>()
            for (i in 1..columnCount) {
                rowData[md.getColumnName(i)] = rs.getObject(i)
            }
            list.add(rowData)
        }
        return list
    }
}
