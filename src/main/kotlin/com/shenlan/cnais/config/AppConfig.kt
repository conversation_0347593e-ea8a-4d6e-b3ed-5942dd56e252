package com.shenlan.cnais.config

import com.alibaba.fastjson.JSONObject
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JavaType
import java.text.SimpleDateFormat
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.google.gson.Gson
import com.shenlan.cnais.ais.*
import com.shenlan.cnais.ais.client.TcpClientManager
import com.shenlan.cnais.ais.client.WebClientUtil.getFromTypeByUniqueId
import com.shenlan.cnais.ais.client.model.ZhejiangResponse
import com.shenlan.cnais.ais.server.beidou.BDShipManager
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatManager
import com.shenlan.cnais.auto.*
import com.shenlan.cnais.mybatis.interceptor.PageInterceptor
import com.shenlan.cnais.util.*
import dk.dma.ais.message.AisMessage
import org.apache.commons.io.FileUtils
import org.apache.commons.io.IOUtils
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.mina.core.buffer.IoBuffer
import org.apache.mina.core.session.IoSession
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import org.springframework.transaction.annotation.TransactionManagementConfigurer
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpInputMessage
import org.springframework.http.HttpOutputMessage
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.converter.json.MappingJacksonInputMessage
import org.springframework.jdbc.datasource.DataSourceTransactionManager
import org.springframework.jms.annotation.JmsListener
import org.springframework.jms.listener.DefaultMessageListenerContainer
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.stereotype.Component
import org.springframework.web.socket.server.standard.ServerEndpointExporter
import java.io.File
import java.io.IOException
import java.lang.reflect.Type
import java.util.*
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import javax.sql.DataSource
import kotlin.concurrent.thread


/**
 * Created by Administrator on 2018/10/25.
 */
open class CustomObjectMapper : ObjectMapper() {
    init {
        //设置日期转换yyyy-MM-dd HH:mm:ss
        dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm")
        disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
        setSerializationInclusion(JsonInclude.Include.NON_NULL)
    }
}

@Configuration
class AppConfig {

    @Bean
    fun objectMapper(): ObjectMapper = CustomObjectMapper()

    @Bean
    fun pageInterceptor() = PageInterceptor()


    // 默认线程池大小为1 也就是说任务都是顺序执行
    @Bean
    fun taskScheduler(): TaskScheduler = ThreadPoolTaskScheduler().apply { poolSize = 15 }

    @Bean
    fun restTemplate(builder: RestTemplateBuilder) = builder.build()

    @Bean
    fun mappingJackson2HttpMessageConverter(): MappingJackson2HttpMessageConverter {
        val jsonConverter = MappingJackson2HttpMessageConverter2()
        val objectMapper = objectMapper()
        jsonConverter.objectMapper = objectMapper
        return jsonConverter
    }

    @Scheduled(fixedDelay = (60 * 1000).toLong())
    fun insertLog() {
        try {
            if (accessList.isNotEmpty()) {
                getBean(AccesslogMapper::class.java).insertList(accessList)
                accessList.clear()
            }
        } catch (e: Exception) {
            accessList.clear()
            errorlog(e)
        }
    }

//    @Scheduled(fixedDelay = (5 * 60 * 1000).toLong())
//    fun stationIfNormal(){//每隔5分钟 检查
//        StationManageUtil.ifAllNormal()
//    }
    companion object {
        var accessList = ArrayList<Accesslog>()
    }
}
@Configuration
class WebSocketConfig {
    @Bean
    fun serverEndpointExporter() = ServerEndpointExporter()
}
@Configuration
@EnableTransactionManagement
class MybatisConfiguration : TransactionManagementConfigurer {
    @Autowired
    lateinit var dataSource: DataSource

    override fun annotationDrivenTransactionManager(): PlatformTransactionManager {
        return DataSourceTransactionManager(dataSource)
    }
}

class MappingJackson2HttpMessageConverter2 : MappingJackson2HttpMessageConverter() {

    @Throws(IOException::class, HttpMessageNotReadableException::class)
    override fun read(type: Type, contextClass: Class<*>?, inputMessage: HttpInputMessage): Any {
        if (!AppPro.aes) {
            return super.read(type, contextClass, inputMessage)
        } else {
//            var a = System.currentTimeMillis()
            val javaType = getJavaType(type, contextClass)
            var rlt = readJavaType(javaType, inputMessage)
//            println((System.currentTimeMillis() - a).toString() + "ms 解密")
            return rlt
        }
    }


    private fun readJavaType(javaType: JavaType, inputMessage: HttpInputMessage): Any {
        try {
            if (inputMessage is MappingJacksonInputMessage) {
                val deserializationView = inputMessage.deserializationView
                if (deserializationView != null) {
                    return this.objectMapper.readerWithView(deserializationView).forType(javaType).readValue(inputMessage.getBody())
                }
            }
            return this.objectMapper.readValue(AesUtil.aesDecrypt(JSONObject.parseObject(IOUtils.toString(inputMessage.body, "UTF-8")).getString(AesUtil.PARAMS)), javaType)
            // return this.objectMapper.readValue(inputMessage.getBody(), javaType);
        } catch (ex: JsonProcessingException) {
            throw HttpMessageNotReadableException("JSON parse error: " + ex.originalMessage, ex)
        } catch (ex: IOException) {
            throw HttpMessageNotReadableException("I/O error while reading input message", ex)
        }

    }

    override fun writeInternal(`object`: Any, type: Type?, outputMessage: HttpOutputMessage) {
        if (!AppPro.aes) {
            super.writeInternal(`object`, type, outputMessage)
        } else {
//            var a = System.currentTimeMillis()
            outputMessage.body.write(AesUtil.aesEncrypt(customObjectMapper.writeValueAsString(`object`)).toByteArray())
//            println((System.currentTimeMillis() - a).toString() + "ms 加密")
        }
    }
}

@Component
object OpenMinaServer {
    // 推送ais数据，由应用接收更新
//    val aisPositionMinaModel = MinaModel(11112)
    val bdShipDataMinaModel = MinaModel(11112)
    var manager: OceanBoatManager? = null
//    var bdShipManager: BDShipManager? = null
    val bdShipManagerMap = mutableMapOf<Int, BDShipManager>()
    val tcpClientManagerList = mutableListOf<TcpClientManager>()

    fun init() {
//        aisPositionMinaModel.start()
//        manager = OceanBoatManager(11113)
//        bdShipManager = BDShipManager(4014)
        bdShipManagerMap[4020] = BDShipManager(4020)
        bdShipManagerMap[4021] = BDShipManager(4021)
        bdShipManagerMap[4022] = BDShipManager(4022)
        bdShipManagerMap[4023] = BDShipManager(4023)
        bdShipManagerMap[4024] = BDShipManager(4024)
        bdShipManagerMap[4025] = BDShipManager(4025)

        tcpClientManagerList.add(TcpClientManager("************", 22202, getFromTypeByUniqueId("Zhejiang"), ZhejiangResponse::class.java))
//        tcpClientManagerList.add(TcpClientManager("127.0.0.1", 22202, ZhejiangResponse::class.java))
        bdShipDataMinaModel.start("GBK")
    }
}
