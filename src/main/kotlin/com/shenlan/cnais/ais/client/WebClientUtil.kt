package com.shenlan.cnais.ais.client

import com.shenlan.cnais.ais.DataFlag
import com.shenlan.cnais.ais.RawData
import com.shenlan.cnais.ais.StationManageUtil
import com.shenlan.cnais.ais.WebClientResponse
import com.shenlan.cnais.ais.server.beidou.BDShipUtil.copyWithRelatedProperties
import com.shenlan.cnais.util.jacksonObjectMapper
import com.shenlan.cnais.util.jacksonObjectMapperIgnoreCase
import com.shenlan.cnais.util.log
import com.shenlan.cnais.util.toJsonString
import org.reflections.Reflections
import org.springframework.stereotype.Component
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

@Component
object WebClientUtil {
    var responseClassesMap = mapOf<String, Class<*>>()

    init {
        responseClassesMap = Reflections("com.shenlan.cnais.ais").getTypesAnnotatedWith(WebClientResponse::class.java).associateBy {
            val annotation = it.getAnnotation(WebClientResponse::class.java)
            annotation.uniqueId
        }
    }

    /**
     *  * 13	FROMTYPE	数据来源	13:广西（北斗）;14:海南（北斗）;15:广东（北斗）;16:福建（北斗）;17:浙江（北斗）;18:上海（北斗）;19:江苏（北斗）;20:山东（北斗）;21:河北（北斗）;22:辽宁（北斗）;23:天津（北斗）
     *
     */
    fun getFromTypeByUniqueId(uniqueId: String): String {
        return when (uniqueId) {
            "Guangxi" -> "13"
            "Hainan" -> "14"
            "Guangdong" -> "15"
            "Fujian" -> "16"
            "Zhejiang" -> "17"
            "Shanghai" -> "18"
            "Jiangsu" -> "19"
            "Shandong" -> "20"
            "Hebei" -> "21"
            "Liaoning" -> "22"
            "Tianjin" -> "23"
            else -> ""
        }
    }

    fun fetchData(manager: WebClientManager, endpoint: String, params: MutableMap<String, String> = mutableMapOf()): Any? {
        var result: Any? = null
        try {
            // 根据 uniqueId 获取对应的 Class
            val responseClass = responseClassesMap[manager.responseUniqueId]
                ?: throw IllegalArgumentException("No class found for uniqueId: ${manager.responseUniqueId}")
            val fromType = getFromTypeByUniqueId(manager.responseUniqueId)

            if (fromType.isEmpty()) throw IllegalArgumentException("No class found for uniqueId: ${manager.responseUniqueId}")

            log.info("${manager.responseUniqueId} start fetch data, fromType: $fromType, endpoint: $endpoint, params: $params")

            val data = manager.webClient?.get()
                ?.uri {
                    it.path(endpoint)
                    params.forEach { (k, v) ->
                        it.queryParam(k, v)
                    }
                    it.build()
                }
                ?.retrieve()
                ?.bodyToMono(String::class.java)
                ?.block() ?: ""

            val list = try {
                if (responseClass.getAnnotation(WebClientResponse::class.java).isList) {
                    val listType = jacksonObjectMapperIgnoreCase.typeFactory.constructCollectionType(List::class.java, responseClass)
                    result = jacksonObjectMapperIgnoreCase.readValue(data, listType)
                    result as List<*>
                } else {
                    result = jacksonObjectMapper.readValue(data, responseClass)
                    getDataList(result) as List<*>
                }
            } catch (e: Exception) {
                log.error("Error parsing uniqueId: ${manager.responseUniqueId}, ${e.message}")
                emptyList<Any>()
            }

            log.info("WebClientManager ${manager.responseUniqueId} data size: ${list.size}")

            offer(manager, list, fromType)
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    fun getDataList(any: Any): Any? {
        any::class.memberProperties.forEach { prop ->
            prop.isAccessible
            if (prop.javaField?.getAnnotation(DataFlag::class.java) != null) {
                return if (prop.javaField?.getAnnotation(DataFlag::class.java)!!.flag) {
                    prop.getter.call(any)
                } else
                    getDataList(prop.getter.call(any)!!)
            }
        }
        return null
    }

    fun offer(manager: WebClientManager, list: List<*>, fromType: String) {
        list.forEach {
            if (it == null) return@forEach
            val bdShipData = copyWithRelatedProperties(it)
            val rawData = RawData().apply {
                hash = bdShipData.toJsonString.hashCode()
                this.fromType = fromType
                date = bdShipData.time
                this.bdShipData = bdShipData
            }

            if (!manager.repeatQueue.contains(rawData.hash)) {
                if (!manager.loading)
                    StationManageUtil.parent?.offer(rawData)
                manager.repeatQueue.add(rawData.hash)
            }
        }
    }


}