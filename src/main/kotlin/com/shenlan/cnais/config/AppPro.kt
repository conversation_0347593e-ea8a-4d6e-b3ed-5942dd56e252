package com.shenlan.cnais.config

import com.shenlan.cnais.auto.LoginCode
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component
import java.io.File
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*


@ConfigurationProperties(prefix = "application", ignoreUnknownFields = true)
@Component
object AppPro {

//    var projectName = "cnais"  //这个是用来自动部署的时候的文件夹名和数据库名 MybatisUtil中使用
    var active: String = "dev"//getBean(AppPro::class.java).active
    fun isDev() = active == "dev"
    val uploadPathFolder = "upload"
    val uploadPath = System.getProperty("user.dir") + File.separator + uploadPathFolder + File.separator
    var aes = false
    var receiveMq=false
    var crlf="" //测试自己拼的串要不要加\r\n  结论自己不用加\r\n
    var mqUrl="tcp://192.168.1.15:61616"
    var chUrl = "******************************************************************"
    var ifStartLog=false
    var split_char="~"
    var testStation="192.168.58.21:8030;192.168.58.21:8030"
    var hiFleetUrl="198.12.171.236:9010"
    var smsUrl = "198.12.171.236:8091"
}

object Constants {
    var nowsharding = ""
        get() = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"))

    var presharding = ""
        get() = LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyyMM"))

    var loginCodeMap = hashMapOf<String, LoginCode>() //验证码登录key=手机号码(账号),扫码登录key=uuid
}

