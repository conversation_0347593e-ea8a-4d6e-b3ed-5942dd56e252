package com.shenlan.cnais.ais.client.model

import com.shenlan.cnais.ais.RelatedProperty
import com.shenlan.cnais.ais.WebClientResponse
import com.shenlan.cnais.util.dateTimeFormatter
import com.shenlan.cnais.util.timestampToLocalDateTime

/**
 * {
 *   "id": "0379943e4bb79f38c367503bd13080ae",
 *   "cardNo": "4201025",
 *   "shipName": "娴峰贰0751",
 *   "positionTime": 1747217772,
 *   "lon": 7304500,
 *   "lat": 1721683,
 *   "fromType": 17
 * }
 */
@WebClientResponse("Zhejiang")
class ZhejiangResponse {
    @RelatedProperty("id") var id: String = ""
    @RelatedProperty("mmsi") var cardNo: String = ""
    @RelatedProperty("name") var shipName: String = ""
    var positionTime: Long = 0
    @RelatedProperty("lng") var lon: Double = 0.0
        get() = field / 100000.0
    @RelatedProperty("lat") var lat: Double = 0.0
        get() = field / 100000.0
    @RelatedProperty("time") var time: String = ""
        get() = dateTimeFormatter.format(timestampToLocalDateTime(positionTime, false))
    var fromType: Int = 17
}