package com.shenlan.cnais.auto

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.aliyuncs.CommonRequest
import com.aliyuncs.DefaultAcsClient
import com.aliyuncs.http.MethodType
import com.aliyuncs.profile.DefaultProfile
import com.shenlan.Result
import com.shenlan.cnais.ais.RawData
import com.shenlan.cnais.ais.StationManageUtil
import com.shenlan.cnais.ais.client.model.HeilongjiangAlarmRequest
import com.shenlan.cnais.ais.client.model.HeilongjiangPositionRequest
import com.shenlan.cnais.ais.client.model.HeilongjiangResponse
import com.shenlan.cnais.ais.server.beidou.BDShipUtil.copyWithRelatedProperties
import com.shenlan.cnais.config.AppPro
import com.shenlan.cnais.config.Constants
import com.shenlan.cnais.config.User
import com.shenlan.cnais.util.getBean
import com.shenlan.cnais.util.getUser
import com.shenlan.cnais.util.log
import com.shenlan.cnais.util.println
import com.shenlan.cnais.util.sqlMapper
import com.shenlan.cnais.util.toJsonString
import org.apache.commons.beanutils.BeanUtils
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.kafka.config.KafkaListenerEndpointRegistry
import org.springframework.kafka.listener.MessageListenerContainer
import org.springframework.web.bind.annotation.*
import java.util.*
import java.util.function.Consumer

@Mapper
interface SqlMapper {

    @Select("\${sql}")
    fun queryInt(@Param("sql") sql: String): Int?

    @Select("\${sql}")
    fun queryStr(@Param("sql") sql: String): String?

    @Select("\${sql}")
    fun queryMap(@Param("sql") sql: String): HashMap<String, String>

    @Update("\${sql}")
    fun update(@Param("sql") sql: String)

    //    @Select("\${sql}")
//    fun getSql(@Param("sql") sql: String): Any
//
    @Select("\${sql}")
    fun queryListMap(@Param("sql") sql: String): List<HashMap<String, Any>>

    @Select("\${sql}")
    fun <T> queryList(@Param("sql") sql: String): List<T>

    @Select("\${sql}")
    fun <T> query(@Param("sql") sql: String): T?
}

@Mapper
interface OpenMapper {

    @Select("""select id, username,password,mobilephone,(select targetid from tbl_permission b where resourceId=a.id and resourceType='TBL_USERINFO' and targetType = 'TBL_ORGANIZE' order by serialnumber limit 0,1) orgId,
(select rolecode from tbl_permission b,tbl_roleinfo c where b.targetid=c.id and resourceId=a.id and resourceType='TBL_USERINFO' and targetType = 'TBL_ROLEINFO' limit 0,1)
rolecode from tbl_userinfo a where mobilephone=#{param1}
    """)
    fun getUserByMobile(mobile: String): User?

    @Select("""select menucode from tbl_module where id in (
select distinct targetid from tbl_permission where resourceType ='TBL_ROLEINFO' and targetType = 'TBL_MODULE' and resourceId in (
select targetid from tbl_permission where resourceType = "TBL_USERINFO" and  resourceId =#{param1} and targetType = "TBL_ROLEINFO"
))ORDER BY menucode
        """)
    fun getMenuList(userid: String): List<String>
}

//fun sendSms(number: String, code: String): Boolean {
//    try {
//        "".log.info("sendsms")
//        val profile = DefaultProfile.getProfile("cn-hangzhou", "LTAI4FdqhciqpaNSEk1fjAd4", "******************************")
//        val client = DefaultAcsClient(profile)
//        val request = CommonRequest()
//        request.setMethod(MethodType.POST)
//        if (AppPro.active == "prod") {
//            request.setDomain("**************:8091") //dysmsapi.aliyuncs.com
//            "".log.info("setDomain 198")
//        } else {
//            request.setDomain("dysmsapi.aliyuncs.com") //dysmsapi.aliyuncs.com
//            "".log.info("setDomain aliyuncs")
//        }
//        request.setVersion("2017-05-25")
//        request.setAction("SendSms")
//        request.putQueryParameter("RegionId", "cn-hangzhou")
//        request.putQueryParameter("PhoneNumbers", number)
//        request.putQueryParameter("SignName", "深蓝信息")
//        request.putQueryParameter("TemplateCode", "SMS_175330236")
//        request.putQueryParameter("TemplateParam", "{\"code\":\"${code}\"}")
//        val response = client.getCommonResponse(request)
//        "".log.info("aliyun response ${response.data}, mobile:${number}")
//        val code = JSON.parseObject(response.data).get("Code")
//        if (code == "OK") {
//            return true
//        }
//    } catch (e: Exception) {
//        "".log.info(e.message)
//        return false
//    }
//    return false
//}
fun sendSms(number: String, code: String): Boolean {
    try {
        "".log.info("send sms")
        val profile = DefaultProfile.getProfile("cn-hangzhou", "LTAI4FdqhciqpaNSEk1fjAd4", "******************************")
        val client = DefaultAcsClient(profile)
        val request = CommonRequest()
        request.method = MethodType.POST
        request.domain = AppPro.smsUrl
        request.version = "2017-05-25"
        request.action = "SendSms"
        request.putQueryParameter("RegionId", "cn-hangzhou")
        request.putQueryParameter("PhoneNumbers", number)
        request.putQueryParameter("SignName", "深蓝信息")
        request.putQueryParameter("TemplateCode", "SMS_175330236")
        request.putQueryParameter("TemplateParam", "{\"code\":\"${code}\"}")
        val response = client.getCommonResponse(request)
        "".log.info("aliyun response ${response.data}, mobile:${number}")
        val responseCode = JSON.parseObject(response.data)["Code"]
        if (responseCode == "OK") {
            return true
        }
    } catch (e: Exception) {
        "".log.info(e.message)
        return false
    }
    return false
}

@RestController
@RequestMapping("/api/open")
class OpenResource {
    @GetMapping("/csrf")
    fun csrf(): Result = Result.success

    @GetMapping("shutdown")
    fun shutdown() {
//        Application.context?.close()
        log.info("shutdown")
        System.exit(1)
    }

    @GetMapping("getAccount")
    fun getAccount(): Result {
        return Result.getSuccess(getUser())
    }

    @GetMapping("getAllDict")
    fun getAllDict(): Result {
        Dict.dictList = Dict.init()
        return Result.getSuccess(Dict.dictList)
    }

    @GetMapping("getLocationList")
    fun getLocationList() = Result.getSuccess(Dict.initLocationList())

    @GetMapping("/getCode/{number}")
    fun getCode(@PathVariable number: String): Result {
        if (sqlMapper.queryInt("select count(1) from tbl_userInfo WHERE mobilePhone ='$number'") == 0) {
            return Result.getError("手机号码尚未注册")
        }
        var code = (Random().nextInt(9000) + 1000).toString()
        if (!sendSms(number, code)) {
            return Result.getSuccessInfo("验证码发送失败")
        } else {
            Constants.loginCodeMap.set(number, LoginCode("", code, System.currentTimeMillis()))
        }
        return Result.getSuccessInfo("验证码已发送，请注意查收")
    }

    @GetMapping("/checkLogin/{number}/{code}")
    fun checkLogin(@PathVariable number: String, @PathVariable code: String): Result {
        if (sqlMapper.queryInt("select count(1) from tbl_userInfo WHERE mobilePhone ='$number'") == 0) {
            return Result.getError("手机号码尚未注册")
        }

        var roleCode = sqlMapper.queryStr("select (select rolecode from tbl_roleinfo where id=targetid) rolecode from tbl_permission where resourcetype='TBL_USERINFO' and targettype='TBL_ROLEINFO' and resourceid in (select id from tbl_userinfo where mobilephone ='${number}')")
                ?: ""
        if ((roleCode == "1" || AppPro.active != "prod") && code == "1234") {//超级管理员 或者开发模式1234就能登录
            Constants.loginCodeMap.set(number, LoginCode("", "1234", System.currentTimeMillis()))
        } else {
            var loginCode = Constants.loginCodeMap.get(number)
            if (loginCode == null) {
                return Result.getError("请获取验证码")
            } else {
                if (loginCode.code != code) {
                    return Result.getError("验证码不匹配")
                }
                if (loginCode.isTimeOut()) {
                    return Result.getError("验证码已失效，请重新获取验证码")
                }
            }
        }
        return Result.success
    }

//    @GetMapping("/getNavigation")
//    fun getNavigation() = Result.getSuccess(Navi.getNavigation())
//
//    @GetMapping("/getModuleList")
//    fun getModuleList() = Result.getSuccess(Modu.topList)
//
//    @GetMapping("/getWebsocket")
//    fun getWebsocket(): Result {
//        return Result.getSuccess(AppPro.websocketUrl)
//    }
//
//    @GetMapping("/getFrontValue")
//    fun getFrontValue(): Result {
//        var map = mutableMapOf<String, String>()
//        map["websocketUrl"] = AppPro.websocketUrl
//        map["tiandituUrl"] = AppPro.tiandituUrl
//        map["seachartUrl"] = AppPro.seachartUrl
//        map["chinaseachartUrl"] = AppPro.chinaseachartUrl
//        return Result.getSuccess(map)
//    }


//    @Autowired
//    lateinit var sessionRegistry: SessionRegistry

//    @GetMapping("/logoutUser/{userName}")
//    fun logoutUser(@PathVariable userName: String): Result {
//        var sessionRegistry = getBean(SessionRegistry::class.java)
//        sessionRegistry.allPrincipals.forEach { user ->
//            if (user is AppUser) {
//                if (user.username == userName) {
//                    sessionRegistry.getAllSessions(user, false)?.let {
//                        it.forEach {
//                            it.expireNow()
//                            log.info("${userName} logout")
//                        }
//                    }
//                }
//            }
//        }
//        return Result.success
//    }

    //http://***********:8100/api/open/setValue/ifEasyCode/true
    @GetMapping("/setValue/{param}/{value}")
    fun setValue(@PathVariable param: String, @PathVariable value: String): Result {
        BeanUtils.setProperty(AppPro, param, value)
        println(BeanUtils.getProperty(AppPro, param))
        return Result.success
    }

    @PostMapping("executeSql")
    fun executeSql(@RequestBody json: JSONObject): Result {
        val sql = json.getString("sql")
        if (sql.isEmpty()) {
            Result.getError("参数不能为空")
        }
        try {
            return Result.getSuccess(sqlMapper.queryListMap(sql))
        } catch (e: Exception) {
            return Result.getError(e.message!!)
        }
    }

    @GetMapping("/sendSms")
    fun sendSmstest() {
//        sendSms("13242078026", "11")
    }

    @GetMapping("/getAppVersion")
    fun getAppVersion(): Result {
        return Result.getSuccess(sqlMapper.queryStr("select JsonValue from tbl_config where configCode='APP_VERSION'"))
    }

    @GetMapping("/reconnectKafka")
    fun reconnectKafka(): Result {
        val registry = getBean(KafkaListenerEndpointRegistry::class.java)
        registry.listenerContainers.forEach(Consumer { container: MessageListenerContainer ->
            log.info("container: ${container.containerProperties.groupId}, container.isRunning: ${container.isRunning}")
            if (!container.isRunning) {
                container.start()
            }
        })
        return Result.success
    }

    @PostMapping("/pushPosition")
    fun pushPosition(@RequestBody request: HeilongjiangPositionRequest): HeilongjiangResponse {
        return try {
            log.info("接收黑龙江推送数据大小：${request.data.positions.size}")

            request.data.positions.forEach { position ->
                val bdShipData = copyWithRelatedProperties(position)
                bdShipData.mmsi = request.sender
                bdShipData.id = request.sender
                val rawData = RawData().apply {
                    hash = bdShipData.toJsonString.hashCode()
                    fromType = "24"
                    date = bdShipData.time
                    this.bdShipData = bdShipData
                }
                StationManageUtil.parent?.offer(rawData)
            }

            HeilongjiangResponse(
                data = null,
                time = Date().time,
                rtnMsg = "成功",
                rtnCode = "0"
            )
        } catch (e: Exception) {
            log.error("黑龙江推送数据失败，${e.message}")
            HeilongjiangResponse(
                data = null,
                time = Date().time,
                rtnMsg = "参数不合法!",
                rtnCode = "E9993"
            )
        }
    }

    @PostMapping("/alarm")
    fun alarm(@RequestBody request: HeilongjiangAlarmRequest): HeilongjiangResponse {
        return try {
            log.info("接收黑龙江报警内容：${request.toJsonString}")
            HeilongjiangResponse(
                data = null,
                time = Date().time,
                rtnMsg = "成功",
                rtnCode = "0"
            )
        } catch (e: Exception) {
            log.error("黑龙江报警失败，${e.message}")
            HeilongjiangResponse(
                data = null,
                time = Date().time,
                rtnMsg = "参数不合法!",
                rtnCode = "E9993"
            )
        }
    }
}

//@RestController
//@RequestMapping("/api/operator")
//class OperatorResource {
//    @GetMapping("getStatus")
//    fun getStatus(): Result = Result.success
//
//    //关闭系统
////    var password = "{\"password\":\"SHENLAN@2016\"}"
//    @GetMapping("shutdown")
//    fun shutdown() {
////        log.info("shutdown")
////        if (json.toString().equals(password)) {
//        Application.context?.close()
////        }
//    }
//}