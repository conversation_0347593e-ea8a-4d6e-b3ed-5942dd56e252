package com.shenlan.cnais.ais.server.beidou

import com.shenlan.cnais.ais.Rate

class BDShipLoginRequest {
    var username: String = ""
    var password: String = ""
}

/**
 * 静态数据
 * 序号	字段	内容	说明
 * 1	ID	报文号及来源	固定值，置为BD
 * 2	CARD	北斗卡号	字符，没有该字段则输出空值
 * 3	TIME	报文接收时间	北京时间，YYYY-MM-DD HH:mm:ss
 * 4	MMSI	海上移动业务识别	本船只的用户识别码，9位数字
 * 5	EID	扩展识别码	字符，用于和MMSI结合识别目标，没有该字段则输出空值
 * 6	IMO	国际海事组织号码	7位数字，没有该字段则输出空值
 * 7	CALLSIGN	呼号	字符，没有该字段则输出空值
 * 8	NAME	船名	字符，没有该字段则输出空值
 * 9	TYPE	货物/船舶类型	如表2所示
 * 10	LOA	船长	船舶尺寸(单位：米)，没有该字段则输出空值
 * 11	BM	船宽	船舶尺寸(单位：米)，没有该字段则输出空值
 * 12	ETA	预计到达时间	MMDDHHMM UTC
 * 比特19-16：月；1-12；0=不可用=默认值
 * 比特15-11：天；1-31；0=不可用=默认值
 * 比特10-6：时；0-23；24=不可用=默认值
 * 比特5-0：分；0-59；60 =不可用=默认值
 * 没有该字段则输出空值
 * 13	DRAUGHT	目前最大静态吃水	单位1/10 米，
 * 255：吃水25.5 m或更大；没有该字段则输出空值
 * 14	DEST	目的地	字符，没有该字段则输出空值
 * 15	EPFS	定位装置类型	0=不可用=默认值
 * 1=GPS
 * 2=GLONASS
 * 3=组合GNSS
 * 4=Loran
 * 5=Chayka
 * 6=INS
 * 7=手动输入或勘测位置
 * 8=Galileo
 * 9=BDS
 * 12=综合PNT系统
 * 13=惯导
 * 14=地面无线电导航
 * 15=内置GNSS
 * 没有该字段则输出空值
 * 16	MID	制造商ID	字符，没有该字段则输出空值
 * 17	UMC	设备型号	字符/数字，没有该字段则输出空值
 * 18	SN	序列号	数字，没有该字段则输出空值
 * 19	FROMTYPE	数据来源	13:广西（北斗）;14:海南（北斗）;15:广东（北斗）;16:福建（北斗）;17:浙江（北斗）;18:上海（北斗）;19:江苏（北斗）;20:山东（北斗）;21:河北（北斗）;22:辽宁（北斗）;23:天津（北斗）
 *
 * 动态数据
 * 序号	字段	内容	说明
 * 1	ID	报文号及来源	固定值，置为BD
 * 2	TIME	报文接收时间	北京时间，YYYY-MM-DD HH:mm:ss
 * 3	MMSI	海上移动业务识别	本船只的用户识别码，9位数字
 * 4	EID	扩展识别码	字符，用于和MMSI结合识别目标，没有该字段则输出空值
 * 5	STATE	航行状态	表示船舶的当前状态，取值范围包括：0（未指定）、1（锚泊中）、2（靠泊中）、3（航行中未受限制）、4（航行中受限制）、5（操作受限）、6（吃水受限）、7（渔业操作中）、8（帆船航行中）、9（拖带作业）、10（超大型拖带作业）、11（驶入港口）、12（船舶操纵能力受限）
 * 没有该字段则输出空值
 * 6	ROT	转向率	单位是度/分钟，表示船舶每分钟的转向角度，
 * 没有该字段则输出空值
 * 7	SOG	对地航速	步长为1/10节（0-102.2节）
 * 1023 = 不可用，1022 = 102.2节或更快，
 * 没有该字段则输出空值
 * 8	LNG	经度	以度为单位的经度
 * 9	LAT	纬度	以度为单位的纬度
 * 10	COG	对地航向	以1/10°为单位（0-3599）。3600 (E10h) = 不可用 = 默认值。
 * 没有该字段则输出空值
 * 11	HEADING	船艏向	度（0-359）（511表明不可用 = 默认值）
 * 没有该字段则输出空值
 * 12	TIMESTAMP	时戳	UTC秒，电子定位系统（EPFS）生成报告的时间（0-59，或在时戳不可用时为60，或在定位系统在人工输入模式下为61，或在电子定位系统工作在估计（航迹推算）模式下为62，或在定位系统不起作用时为63）
 * 没有该字段则输出空值
 * 13	FROMTYPE	数据来源	13:广西（北斗）;14:海南（北斗）;15:广东（北斗）;16:福建（北斗）;17:浙江（北斗）;18:上海（北斗）;19:江苏（北斗）;20:山东（北斗）;21:河北（北斗）;22:辽宁（北斗）;23:天津（北斗）
 */
class BDShipData {
    // 静态数据
    var id: String = ""
    var card: String = ""
    var time: String = ""
    var mmsi: String = ""
    var eid: String = ""
    var imo: String = ""
    var callSign: String = ""
    var name: String = ""
    var type: String = ""
    var loa: String = ""
    var bm: String = ""
    var eta: String = ""
    @Rate(0.1) var draught: String = ""
    var dest: String = ""
    var epfs: String = ""
    var mid: String = ""
    var umc: String = ""
    var sn: String = ""
    var fromType: String = ""

    // 动态数据
    var state: String = ""
    var rot: String = ""
    @Rate(0.1) var sog: String = ""
    var lng: String = ""
    var lat: String = ""
    @Rate(0.1) var cog: String = ""
    var heading: String = ""
    var timestamp: String = ""
}

class BDShipInfo {
    var id: String = ""
    var card: String = ""
    var time: String = ""
    var mmsi: String = ""
    var eid: String = ""
    var imo: String = ""
    var callSign: String = ""
    var name: String = ""
    var type: String = ""
    var loa: Double = 0.0
    var bm: Double = 0.0
    var eta: String = ""
    @Rate(0.1) var draught: Double = 0.0
    var dest: String = ""
    var epfs: String = ""
    var mid: String = ""
    var umc: String = ""
    var sn: String = ""
    var fromType: String = ""
}

class BDPosition {
    var id: String = ""
    var time: String = ""
    var mmsi: String = ""
    var eid: String = ""
    var state: Int = 0
    var rot: Double = 0.0
    @Rate(0.1) var sog: Double = 0.0
    var lng: Double = 0.0
    var lat: Double = 0.0
    @Rate(0.1) var cog: Double = 0.0
    var heading: Double = 0.0
    var timestamp: Int = 0
    var fromType: String = ""
}