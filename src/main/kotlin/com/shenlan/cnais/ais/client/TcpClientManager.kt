package com.shenlan.cnais.ais.client

import com.shenlan.cnais.ais.RawData
import com.shenlan.cnais.ais.StationManageUtil
import com.shenlan.cnais.ais.WebClientResponse
import com.shenlan.cnais.ais.client.WebClientUtil.getFromTypeByUniqueId
import com.shenlan.cnais.ais.server.beidou.BDShipUtil.copyWithRelatedProperties
import com.shenlan.cnais.util.MinaModel
import com.shenlan.cnais.util.jacksonObjectMapper
import com.shenlan.cnais.util.log
import com.shenlan.cnais.util.toJsonString

class TcpClientManager {
    var model: MinaModel? = null
    var clazz: Class<*>? = null
    var type = ""

    constructor()

    constructor(ip: String, port: Int, fromType: String = "", clazz: Class<*>) {
        model = MinaModel(ip, port, true).apply { this.fromType = fromType }
        this.clazz = clazz
        this.type = getFromTypeByUniqueId(this.clazz?.getAnnotation(WebClientResponse::class.java)?.uniqueId ?: "")

        model?.messageReceived = { data, _ ->
            log.info(data)
            val instance = jacksonObjectMapper.readValue(data, this.clazz)
            val bdShipData = copyWithRelatedProperties(instance)
            val rawData = RawData().apply {
                hash = bdShipData.toJsonString.hashCode()
                this.fromType = type
                date = bdShipData.time
                this.bdShipData = bdShipData
            }
            StationManageUtil.parent?.offer(rawData)
        }

        model?.start("GBK")
    }
}