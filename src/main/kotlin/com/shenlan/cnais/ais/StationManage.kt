package com.shenlan.cnais.ais

import ch.hsr.geohash.GeoHash
import com.shenlan.cnais.ais.server.beidou.BDPosition
import com.shenlan.cnais.ais.server.beidou.BDShipInfo
import com.shenlan.cnais.ais.server.beidou.BDShipUtil
import com.shenlan.cnais.auto.*
import com.shenlan.cnais.config.OpenMinaServer.bdShipDataMinaModel
import com.shenlan.cnais.util.*
import dk.dma.ais.message.*
import dk.dma.ais.packet.AisPacket
import dk.dma.ais.packet.AisPacketParser
import io.reactivex.rxjava3.core.Observable
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

//复制基站系统的采集并修改
object StationManageUtil {
    // 各种数据源插入
    var parent: MultiStationManage? = null

    var ifInit = false

    fun init() {
        parent = InitByNodeinfo(Nodeinfo().apply {
            id = "1"
            linkOrgId = "1"
            orgName = "国家"
        })
    }

    fun InitByNodeinfo(nodeInfo: Nodeinfo): MultiStationManage {
        val config = Stationconfig(nodeInfo)
        val manage = MultiStationManage(config).apply {
            init()
        }
        return manage
    }
}

class MultiStationManage : BaseStationManage {
    var port = 11105
    lateinit var stationconfig: Stationconfig
    lateinit var minaModel: MinaModel
    var stationconfigList = mutableListOf<Stationconfig>()
    var repeatSet = mutableSetOf<Int>()
    var repeatList = mutableListOf<Int>()
    var maxSize = 10000
    lateinit var stationStore: StationStore

    var byteCount = 0L //连接后接收到的字符数
    var repeatCount = 0L //重复数据
    var receiveCount = 0L //接收数据个数
    var dealCount = 0L //已处理个数

    //    var blockIndex = 0//commblock的序号从0-10000
    var lastDataTime: Long = System.currentTimeMillis()

    //    var executor: ThreadPoolExecutor = ThreadPoolExecutor(3, 100, 60L, TimeUnit.SECONDS, LinkedBlockingQueue())
    var testNumber = 0L
    var ifUpdate = true

    //开启一个socket服务端

    constructor(stationconfig1: Stationconfig) {
        stationconfig = stationconfig1
        stationStore = StationStore(stationconfig1)
        stationStore.stationconfig = stationconfig1 //        stationconfig.children.forEach {
        stationName = stationconfig.stationName //        port = stationconfig1.startPort.toInt()
    }

    override fun init() { //负责线程的启动等等
        super.init()
        log.info("start MultStationManage server") //        minaModel.start()
        stationStore.init()
        beginLog()
    }

    fun beginLog() {
        Observable.interval(10, 60, TimeUnit.SECONDS).subscribe {
            try {
                getCurrentLog()
            } catch (e: Exception) {
                errorlog(e)
            }
        }
    }

    fun getCurrentLog() { //获取当前日志
//        log.info("${stationName} Stationstore状态 ${stationStore.commThread.isAlive} ${stationStore.dataQueue.size} ${stationStore.dataList.size} 线程状态:${commThread.isAlive}  最后接收数据时间:${intToStr((lastDataTime / 1000).toInt())} 接收数据行数(包含VSI):${receiveCount} 处理数据行数:${dealCount}" + "相差行数:${receiveCount - dealCount} 重复数据个数:${repeatCount} 数据字符:${byteCount} 队列大小:${dataQueue.size}")
    }

    override fun close() {
        log.info("begin close")
        try {
            super.close()
            stationStore.close()
            minaModel.closeServer()
        } catch (e: Exception) {

        }
        log.info("end close")
    }

    override fun doTaskinfoFun() {
        while (commThread.on) {
            try {
                var data = dataQueue.take() //                data.sid = TaskTimer.sidMap[data.stationId] ?: "" //                log.info("doTaskinfoFun: " + data.outData)
                if (stationconfig.isAreaConfig) {       // 新增基站的连接，不直接存储数据，推送给parent
                    StationManageUtil.parent?.offer(data)
                } else {
                    basicRecord(data)
                    if (!ifRepeat(data)) { //                addMarkData(data, data.ifrepeat.toChar())
                        stationStore.offer(data) //                parent?.offer(data)
                    }
                }
                //                writeToClient(data)
            } catch (e: Throwable) {
                errorlog(e)
            }
        }
    }

    fun basicRecord(data: RawData) { //基础记录
        dealCount++
        byteCount += (data.getLength())
    }

    fun ifRepeat(data: RawData): Boolean {
        try {
            if (repeatList.size >= maxSize) { //当数量大于10000 去掉最先的一半数据
                repeatList = getSubList(repeatList) //使用subList会导致java.lang.StackOverflowError
                repeatSet = repeatList.toMutableSet()
            }
            if (repeatSet.contains(data.hash)) {
                repeatCount++
                data.ifrepeat = true
                return true
            } else {
                repeatSet.add(data.hash)
                repeatList.add(data.hash) //java.lang.stackoverflow
            }
        } catch (e: Throwable) {
            errorlog(e)
            repeatList.clear()
            repeatList = mutableListOf()
            repeatSet.clear()
            repeatSet = mutableSetOf()
        }
        return false
    }

    fun getSubList(repeatList: MutableList<Int>): MutableList<Int> {
        return ArrayList<Int>(repeatList.subList(repeatList.size / 2, repeatList.size)).toMutableList() //效率是上面的20倍 因为这个是直接复制内存地址
    }

    override fun offer(data: RawData) { //        log.info("MultStationManage receiveData")
        lastDataTime = System.currentTimeMillis()
        dataQueue.offer(data)
        receiveCount++ //这个要写到offer后面
    }
}

class CommThread(name: String) : Thread(name) {
    open var doTaskinfo = { } //子类重载 执行具体任务
    var on = true //根据这个判断线程是否继续执行
    override fun run() {
        doTask()
    }

    fun doTask() {
        try {
            log.info("$name doTask")
            doTaskinfo()
        } catch (e: Exception) {
            log.error("$name " + e.message)
            error(e)
        }
        log.info("doTask end")
    }

    fun close() {
        log.info("$name close")
        on = false //这个变为false 线程还是会在的 因为线程里都是take 会阻塞在那
        this.interrupt() //为了提醒阻塞的线程
    }
}

//基站的处理抽象而来
open class BaseStationManage {
    var stationName = ""
    var parent: MultiStationManage? = null

    var dataQueue = ArrayBlockingQueue<RawData>(Aisc.QUEUE_SIZE)
    var commThread = CommThread(this.javaClass.name).apply {
        doTaskinfo = {
            doTaskinfoFun()
        }
    }

    open fun init() {
        commThread.start()
    }

    open fun doTaskinfoFun() {

    }

    open fun offer(data: RawData) {
        if (dataQueue.size < Aisc.QUEUE_SIZE) {
            dataQueue.offer(data)
        }
    }

    open fun close() {
        commThread.close()
    }

}

class StationStore : BaseStationManage {
    //基站的数据传递到这里的队列 定时处理队列的数据
    //    var dataQueue = ArrayBlockingQueue<RawData>(Aisc.QUEUE_SIZE)
    var insertSplit = 3 * 1000 //有时间没测成功 不是没测成功 是1s后如果没数据 就会阻塞在那 有数据就会处理
    var insertSize = 100
    var lastTime = System.currentTimeMillis()
    var dataList = mutableListOf<RawData>()
    var stationParse = StationParse()
    var stationconfig: Stationconfig? = null
    var executor: ThreadPoolExecutor = ThreadPoolExecutor(10, 100, 60L, TimeUnit.SECONDS, LinkedBlockingQueue())
    var timeList = arrayListOf<Long>()
    var kafkaAisPositionList = mutableListOf<Aisposition>()

    constructor()
    constructor(stationconfig: Stationconfig) {
        this.stationconfig = stationconfig
    }

    override fun init() {
        log.info("init StationStore")
        super.init()
    }

    override fun doTaskinfoFun() {
        while (commThread.on) {
            try {
                var data = dataQueue.take()
                dataList.add(data)
                if ((System.currentTimeMillis() - lastTime) > insertSplit) { //                    var newList = mutableListOf<RawData>()
                    //                    newList.addAll(dataList)
                    //                    dataList.clear()
                    var newList = dataList
                    dataList = mutableListOf<RawData>()
                    lastTime = System.currentTimeMillis()
                    deal(newList)
                }
            } catch (e: Throwable) { //竟然是virtualatonrecord 是从中海油copy的报名是错的导致getBean失败 开始没加trycatch导致线程失败
                errorlog(e)
            }
        }
    }

    //跑着跑着 队列就满了 应该是这里需要1s的时间 然后改成线程池1个多小时又不更新数据库了 queue大小为0 dataList的数据一直在变化说明deal 没跑看jmc pool-204-thread-3 block
    //竟然是卡在e.printStackTrace() 是系统输出流满了 不能用这个
    private fun deal(newList: MutableList<RawData>) {
        executor.execute {
            val chModel = stationParse.dealDataList(newList)
            ChUtil.insertChmodel(chModel)
        }
    }

}

class StationParse {
    //新增自定义报文 存储具体基站设备id 目的让辖区海区可以看具体基站的船舶报告
    fun dealDataList(rawDataList: MutableList<RawData>): ChModel {
        val chModel = ChModel()
        rawDataList.forEach { rawData ->
            if (rawData.fromType == "12") {
                val upRealLocation = rawData.upRealLocation
                val geohash = GeoHash.withCharacterPrecision(upRealLocation.lat * 0.0001, upRealLocation.lon * 0.0001, 7).toBase32()
                val chPosition = Chposition(
                    intToStr(upRealLocation.utc.toInt()), geohash, upRealLocation.msgId.toString(), upRealLocation.status,
                    0, upRealLocation.speed * 0.1f, 0,  upRealLocation.lat * 0.0001f,
                    upRealLocation.lon * 0.0001f, upRealLocation.course * 0.1f,
                    upRealLocation.trueHeading / 10)
                chPosition.ifClassa = 1
                chPosition.fromType = rawData.fromType
                chModel.aispositionList.add(chPosition)
            } else if (rawData.fromType.toInt() in 13..24) {
                rawData.bdShipData?.let { data ->
                    data.fromType = rawData.fromType
                    bdShipDataMinaModel.writeToClient(data.toJsonString)
                    if (data.lng.toDoubleOrNull() != null) {
                        val position = BDShipUtil.copy(data, BDPosition::class.java) as BDPosition
                        position.fromType = rawData.fromType
                        chModel.bdPositionList.add(position)
                    }
                    if (data.name.isNotEmpty()){
                        val shipInfo = BDShipUtil.copy(data, BDShipInfo::class.java) as BDShipInfo
                        shipInfo.fromType = rawData.fromType
                        chModel.bdShipInfoList.add(shipInfo)
                    }
                }
            }
        }
        return chModel
    }
}
