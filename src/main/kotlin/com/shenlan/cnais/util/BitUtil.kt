package com.shenlan.cnais.util

object BitUtil {
    private val bitWriter = BitWriter(1024)

    fun readLine(line: String): ByteArray {
        bitWriter.reset()
        line.forEach { c ->
            try {
                val fourBit = charToFourBit(c)
                bitWriter.writeBits(fourBit, 4)
            } catch (e: Exception) {
                return@forEach
            }
        }
        return bitWriter.toByteArray()
    }

    fun writeBits(value: Int, n: Int) {
        bitWriter.writeBits(value, n)
    }

    fun toByteArray(): ByteArray {
        return bitWriter.toByteArray()
    }

    fun reset() {
        bitWriter.reset()
    }
}