package com.shenlan.cnais.task

import com.fasterxml.jackson.module.kotlin.readValue
import com.shenlan.cnais.ais.RawData
import com.shenlan.cnais.ais.StationManageUtil
import com.shenlan.cnais.ais.client.ApiRequestConfig
import com.shenlan.cnais.ais.client.WebClientManager
import com.shenlan.cnais.ais.client.WebClientUtil
import com.shenlan.cnais.ais.client.WebClientUtil.getFromTypeByUniqueId
import com.shenlan.cnais.ais.client.model.AuthResponse
import com.shenlan.cnais.ais.client.model.GuangxiResponse
import com.shenlan.cnais.ais.client.model.HainanAuthErrorResponse
import com.shenlan.cnais.ais.client.model.HainanAuthResponse
import com.shenlan.cnais.ais.client.model.HainanResponse
import com.shenlan.cnais.ais.server.beidou.BDShipUtil.copyWithRelatedProperties
import com.shenlan.cnais.util.SM4Util
import com.shenlan.cnais.util.dateFormat
import com.shenlan.cnais.util.dateTimeFormatter
import com.shenlan.cnais.util.jacksonObjectMapper
import com.shenlan.cnais.util.jacksonObjectMapperIgnoreCase
import com.shenlan.cnais.util.localDateFormatter
import com.shenlan.cnais.util.log
import com.shenlan.cnais.util.toJsonString
import io.reactivex.rxjava3.core.Observable
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import util.Sm4Util
import java.security.MessageDigest
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Base64
import java.util.Date
import java.util.concurrent.TimeUnit
import kotlin.collections.set

@Component
object WebClientTask {
    val clientMap = mutableMapOf<String, WebClientManager>()

    fun init() {
        initGuangxi()
        initGuangdong()
        initHainan()
    }

    fun initGuangxi() {
        val webClientManager = WebClientManager("http://198.21.1.78:8173", "Guangxi").apply {
            this.apiRequestConfigList = mutableListOf(
//                ApiRequestConfig("dyFisherInfoNormal/list", mutableMapOf("startTime" to "", "endTime" to "")),
                ApiRequestConfig("/dyPositionNorth/list", mutableMapOf("startTime" to dateTimeFormatter.format(LocalDate.now().atStartOfDay()), "size" to "1000")),
                ApiRequestConfig("/dyPositionSouth/list", mutableMapOf("startTime" to dateTimeFormatter.format(LocalDate.now().atStartOfDay()), "size" to "1000"))
            )
            loading = true
        }

        webClientManager.apiRequestConfigList.forEach { config ->
            Observable.interval(10, 300, TimeUnit.SECONDS)
                .subscribe { tick ->
                    try {
                        val time = dateTimeFormatter.format(LocalDate.now().atStartOfDay())
                        if (config.type == "get") {
                            val response = WebClientUtil.fetchData(webClientManager, config.endpoint, config.params) as GuangxiResponse
                            if (response.data.total > config.params.getOrDefault("size", "0").toInt()) {
                                config.params["size"] = response.data.total.toString()
                                WebClientUtil.fetchData(webClientManager, config.endpoint, config.params)
                            }
                            config.params["startTime"] = time
                            webClientManager.loading = false
                        }
                    } catch (e: Exception) {
                        log.error("Guangxi api ${config.endpoint}, params: ${config.params} error: ${e.message}")
                    }
                }
        }

        clientMap["Guangxi"] = webClientManager
    }

    fun initGuangdong() {
         val webClientManager = WebClientManager("http://api.gd.msa.gov.cn", "Guangdong").apply {
            this.apiRequestConfigList = mutableListOf(
                ApiRequestConfig("/esb/api/proxy/security/token/authorize_token", mutableMapOf("username" to "MSAFish", "password" to "", "ts" to "")),
                ApiRequestConfig("/esb2/80/FishInfoBD", mutableMapOf("token" to ""))
            )
        }

        webClientManager.apiRequestConfigList.find { it.endpoint == "/esb/api/proxy/security/token/authorize_token" }?.let { config ->
            Observable.interval(10, 25 * 60, TimeUnit.SECONDS)
                .subscribe { tick ->
                    try {
                        val password = "MSA@2025Fish"
                        val md5 = MessageDigest.getInstance("MD5")
                        val hash = md5.digest(password.toByteArray()).joinToString("") { "%02x".format(it) }.toUpperCase()
                        val time = dateTimeFormatter.format(LocalDateTime.now())
                        config.params["password"] = md5.digest("$hash$time".toByteArray()).joinToString("") { "%02x".format(it) }.toUpperCase()
                        config.params["ts"] = time

                        val data = webClientManager.webClient?.get()
                            ?.uri {
                                it.path(config.endpoint)
                                config.params.forEach { (k, v) ->
                                    it.queryParam(k, v)
                                }
                                it.build()
                            }
                            ?.retrieve()
                            ?.bodyToMono(String::class.java)
                            ?.block() ?: ""

                        val token = (jacksonObjectMapper.readValue<AuthResponse>(data) as AuthResponse).result.token
                        log.info("Guangdong token: $token")
                        webClientManager.apiRequestConfigList.find { it.endpoint == "/esb2/80/FishInfoBD" }?.params?.set("token", token)
                    } catch (e: Exception) {
                        log.error("fetch Guangdong token error: ${e.message}")
                    }
                }
        }

        webClientManager.apiRequestConfigList.find { it.endpoint == "/esb2/80/FishInfoBD" }?.let { config ->
            Observable.interval(20, 5 * 60, TimeUnit.SECONDS)
                .subscribe { tick ->
                    WebClientUtil.fetchData(webClientManager, config.endpoint, config.params)
                }
        }

        clientMap["Guangdong"] = webClientManager
    }

    fun initHainan() {
        val webClientManager = WebClientManager("http://*************:8001", "Hainan").apply {
            this.apiRequestConfigList = mutableListOf(
                ApiRequestConfig("/blade-auth/oauth/token"),
                ApiRequestConfig("shipReg/hnFishShipAll")
            )
            this.params["startTime"] = dateTimeFormatter.format(LocalDateTime.now()).replace(" ", "%20")
            this.params["pageSize"] = "10000"
        }

        webClientManager.apiRequestConfigList.find { it.endpoint == "/blade-auth/oauth/token" }?.let { config ->
            Observable.interval(10, 100 * 60, TimeUnit.SECONDS)
                .subscribe { tick ->
                    updateHainaiToken(config)
                }
        }

        webClientManager.apiRequestConfigList.find { it.endpoint == "shipReg/hnFishShipAll" }?.let { config ->
            Observable.interval(30, 5 * 60, TimeUnit.SECONDS)
                .subscribe { tick ->
                    try {
                        val time = LocalDateTime.now()
                        webClientManager.params["endTime"] = dateTimeFormatter.format(time).replace(" ", "%20")
                        val plainText = "shipReg/hnFishShipAll?startTime=${webClientManager.params["startTime"]}&endTime=${webClientManager.params["endTime"]}&pageSize=${webClientManager.params["pageSize"]}#${localDateFormatter.format(LocalDate.now())}#a90542f8bad34aebb03645246f54c96e"
                        val cipherText = Sm4Util.encryptEcb("c1e3ff7bfb3f4afebdacad941bf00bc7", plainText)
                        log.info("Hainan plainText: $plainText, endpoint: /api/$cipherText")
                        val data = webClientManager.webClient?.get()
                            ?.uri("/api/$cipherText")
                            ?.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                            ?.header("Authorization", "Basic YTkwNTQyZjhiYWQzNGFlYmIwMzY0NTI0NmY1NGM5NmU6JDJhJDEwJEtvaURkRkZOVy54OUdkYkNJRzlna2U4VjNKcFptQjFxZXBRR3l6TlN5bG5zVHo1T1dTblFt")
                            ?.header("Blade-auth", "bearer ${(webClientManager.authResponse!! as HainanAuthResponse).access_token}")
                            ?.header("appid", "a90542f8bad34aebb03645246f54c96e")
                            ?.exchange()
                            ?.flatMap { response ->
                                response.bodyToMono(String::class.java)
                            }
                            ?.block() ?: ""

                        try {
                            val list = jacksonObjectMapperIgnoreCase.readValue(data, HainanResponse::class.java).result

                            log.info("Hainan data size: ${list.size}")

                            if (list.size > (webClientManager.params["pageSize"]?.toIntOrNull() ?: 0))
                                webClientManager.params["pageSize"] = (list.size + 500).toString()

                            WebClientUtil.offer(webClientManager, list, getFromTypeByUniqueId(webClientManager.responseUniqueId))
                            webClientManager.params["startTime"] = dateTimeFormatter.format(time).replace(" ", "%20")
                        } catch (e: Exception) {
                            log.error("Hainan parse data error: ${e.message}")
                            updateHainaiToken(clientMap["Hainan"]!!.apiRequestConfigList.find { it.endpoint == "/blade-auth/oauth/token" }!!)
                        }
                    } catch (e: Exception) {
                        log.error("Hainan fetch data error: ${e.message}")
                    }
                }
        }

        clientMap["Hainan"] = webClientManager
    }

    fun updateHainaiToken(config: ApiRequestConfig) {
        try {
            val webClientManager = clientMap["Hainan"]!!
            val data = webClientManager.webClient?.post()
                ?.uri(config.endpoint)
                ?.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                ?.header("Authorization", "Basic YTkwNTQyZjhiYWQzNGFlYmIwMzY0NTI0NmY1NGM5NmU6JDJhJDEwJEtvaURkRkZOVy54OUdkYkNJRzlna2U4VjNKcFptQjFxZXBRR3l6TlN5bG5zVHo1T1dTblFt")
                ?.exchange()
                ?.flatMap { response ->
                    response.bodyToMono(String::class.java)
                }
                ?.block() ?: ""

            val authResponse = try {
                (jacksonObjectMapper.readValue<HainanAuthResponse>(data) as HainanAuthResponse)
            } catch (e: Exception) {
                log.warn("Hainan auth warning: $data")
                val authErrorResponse = (jacksonObjectMapper.readValue<HainanAuthErrorResponse>(data) as HainanAuthErrorResponse)
                HainanAuthResponse().apply { access_token = authErrorResponse.token }
            }
            log.info("Hainan token response: ${authResponse.toJsonString}")
            webClientManager.authResponse = authResponse
        } catch (e: Exception) {
            log.error("fetch Hainan token error: ${e.message}")
        }
    }
}