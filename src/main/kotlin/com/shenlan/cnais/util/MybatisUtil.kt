package com.shenlan.cnais.util

import com.shenlan.cnais.config.AppPro
import org.apache.commons.beanutils.BeanUtils
import org.apache.commons.io.IOUtils
import org.apache.ibatis.io.Resources
import org.apache.ibatis.session.SqlSession
import org.apache.ibatis.session.SqlSessionFactory
import org.apache.ibatis.session.SqlSessionFactoryBuilder
import org.yaml.snakeyaml.Yaml
import java.lang.reflect.Method
import java.nio.charset.Charset
import java.sql.Connection
import java.sql.DriverManager
import java.util.*
import java.util.concurrent.TimeUnit


/**
 * Created by Administrator on 2019/8/23.
 */
object MybatisUtil {
    private var sessionFactory: SqlSessionFactory? = null
    val sqlSession: SqlSession
        get() = getSqlSessionFactory()!!.openSession(true)

    fun getSqlSessionFactory(): SqlSessionFactory? {
        if (sessionFactory == null) {
            val resource = "mybatis-config.xml"
            val inputStream = Resources.getResourceAsStream(resource)
            sessionFactory = SqlSessionFactoryBuilder().build(inputStream, "dev")
        }

        return sessionFactory
    }

    var sessionFactory2: SqlSessionFactory? = null
//    val sqlSession2: SqlSession
//        get() = getSqlSessionFactory2()!!.openSession(true)

    fun getSqlSessionFactory2(): SqlSessionFactory? {
        if (sessionFactory2 == null) {
            val resource = "mybatis-config2.xml"
            val inputStream = Resources.getResourceAsStream(resource)
            sessionFactory2 = SqlSessionFactoryBuilder().build(inputStream, "dev")
        }
        return sessionFactory2
    }

    fun getSqlSession2(id: String): SqlSession {
        val resource = "mybatis-config2.xml"
        val inputStream = Resources.getResourceAsStream(resource)
        val sessionFactory2 = SqlSessionFactoryBuilder().build(inputStream, id)
        return sessionFactory2.openSession()
    }

}

//class MyLanguageDriver : XMLLanguageDriver() {
//
//    override fun createSqlSource(configuration: Configuration,
//                                 script: String, parameterType: Class<*>): SqlSource {
//        var script1 = script
//        if (Sql.sqlMap.containsKey(script)) {
//            script1 = Sql.sqlMap[script]!!
//            println("replace sql")
//        }
//        return super.createSqlSource(configuration, script1, parameterType)
//    }
//}
//
fun main(args: Array<String>) {
//    BaseMapper::class.java.methods
    //保存所有有表的getInfo getList delete insertList insert getListByPid sql语句
    Sql.init()
    //打印出sql语句
    Sql.printSql("Atonsheet")
//    MybatisUtil.sqlSession.getMapper(UserinfoMapper::class.java).getList().pj()
}

object Sql {
    var columnMap = hashMapOf<String, MutableList<String>>()
    var sqlMap = hashMapOf<String, String>()
//    var databaseName = AppPro.projectName
    var devUrl = ""
        get() = BeanUtils.getProperty(Yaml().load<Map<String, Any>>(Resources.getResourceAsStream("config/application-dev.yml")), "spring.datasource.url")
    var prodUrl = ""
        get() = BeanUtils.getProperty(Yaml().load<Map<String, Any>>(Resources.getResourceAsStream("config/application-prod.yml")), "spring.datasource.url")

    //子类重载 去除父类的方法
    fun removeMethod(methods: Array<Method>): Array<Method> {
        var names = mutableListOf<String>()
        methods.forEach {
            if (!it.isBridge) {
                if (!it.toString().contains("BaseMapper")) {
                    names.add("BaseMapper." + it.name)
                }
            }
        }
        var methodsList = mutableListOf<Method>()
        methods.forEach {
            if (!it.isBridge) {
                var flag = false
                names.forEach { name ->
                    if (it.toString().contains(name)) {
                        flag = true
                    }
                }
                if (flag == false) {
                    methodsList.add(it)
                }
            }
        }
        return methodsList.toTypedArray()
    }

    fun init() {
        log.info("init Sql begin")
        initColumnMap()
        initSqlMap()
        log.info("init Sql end")
    }

    fun replaceSqlByMapperName(sql: String, mapperName: String, methodName: String): String {
        var key = mapperName.replace("Mapper", "") + ".${methodName}"
//        log.info("sql:${sql} mapperName: ${mapperName} methodName: ${methodName}, key: ${key}")

        if (sql.startsWith("BaseModel")) {
//            println(sqlMap.size)
            if (sqlMap.containsKey(key)) {
//                log.info("replace sql ${sql} ->${sqlMap[key]}")
                return sqlMap[key]!!
            } else {
                return sql
            }
        }
        return sql
    }

    fun printSql(modelName: String = "Visibility") {
        sqlMap.forEach { t, u ->
            if (t.contains(modelName)) {
                println(t)
                println(u)
                println("======")
            }
        }
        Date()
    }

//    fun ifProd(): Boolean {
//        return getIp().joinToString(",").contains("198.12")
//    }

    fun ifProd(): Boolean {//以前根据正式环境ip判断 发现每次要改ip 修改成根据配置文件
        try {
            var str = IOUtils.toString(Resources.getResourceAsStream("config/application.yml"), Charset.defaultCharset())
            if (str.contains("active: prod")) {
                return true
            }
        } catch (e: Exception) {
            return false
        }
        return false
    }

    fun initColumnMap() {//如果机房整改 整个断电重启 11号机启的慢 那么12号机的自启动还是会失败
        var databaseName = ""
        if (ifProd()) {
            databaseName = prodUrl.split("?")[0].split("/")[3]
        } else {
            databaseName = devUrl.split("?")[0].split("/")[3]
        }
        var sql = "SELECT CONCAT(table_name,';',column_name) a FROM information_schema.columns WHERE table_schema='${databaseName}' AND table_name NOT LIKE '%20%'"
//        var clientSession = MybatisUtil.sqlSession
//        var st = clientSession.connection.createStatement()
        //因为这个要在springboot 之前就要加载好 不能获取 是开发还是生产模式 就通过异常的方式比较简单
        Class.forName("com.mysql.jdbc.Driver")

        var conn: Connection? = null
        var flag = false
        while (flag == false) {
            try {
                log.info("connect to db ${if (ifProd()) prodUrl else devUrl}")
                conn = DriverManager.getConnection(if (ifProd()) prodUrl else devUrl, "root", "SHENLAN@2016")
                flag = true
            } catch (e: Exception) {
                log.info(e.message)
                TimeUnit.SECONDS.sleep(10)
            }
        }
        var st = conn!!.createStatement()
        var rs = st.executeQuery(sql)
        var list = ArrayList<String>()
        while (rs.next()) {
            list.add(rs.getString("a"))
        }
        rs.close()
        st.close()
        conn.close()
//        var list = clientSession.getMapper(BaseMapper::class.java).getSqlList(sql) as List<String>
//        clientSession.close()
        var excludeColumn = listOf<String>("sysCreated", "sysUpdated", "sysDeleted")
        list.forEach {
            var str = it.split(";")
            var modelName = str[0].toLowerCase().replace("tbl_", "").firstUpper()
            if (!excludeColumn.contains(str[1])) {
                if (columnMap.containsKey(modelName)) {
                    columnMap[modelName]!!.add(str[1])
                } else {
                    columnMap.put(modelName, mutableListOf(str[1]))
                }
            }
        }
    }

    fun initSqlMap() {
        columnMap.forEach { modelName, u ->
            sqlMap.put("${modelName}.getList", getList(modelName))
            sqlMap.put("${modelName}.getInfo", getInfo(modelName))
            sqlMap.put("${modelName}.insert", insert(modelName))
            sqlMap.put("${modelName}.delete", delete(modelName))
            sqlMap.put("${modelName}.deleteLogic", deleteLogic(modelName))
            sqlMap.put("${modelName}.getListByPid", getListByPid(modelName))
            sqlMap.put("${modelName}.insertList", insertList(modelName))
        }
    }

    //就只有一两个字段不需要 用*
    fun getList(modelName: String): String {
//        return "select ${columnMap[modelName]!!.joinToString(",")} from tbl_${modelName} where 1=1"
        return "select * from tbl_${modelName} where 1=1 and sysDeleted = 0"
    }

    fun getInfo(modelName: String): String {
//        return "select ${columnMap[modelName]!!.joinToString(",")} from tbl_${modelName} where id=#{param1}"
        return "select * from tbl_${modelName} where id=#{param1}"
    }

    fun insert(modelName: String): String {
        var columns = columnMap[modelName]!!
        var selectColumn = columns.joinToString(",")
        var insertColumnValue = columns.map { "#{${it},jdbcType=VARCHAR}" }.joinToString(",")
        return "insert into tbl_${modelName} (${selectColumn}) values (${insertColumnValue})"
    }

    fun delete(modelName: String): String {
        return "delete from tbl_${modelName} where id = #{param1}"   //物理删除
    }

    fun deleteLogic(modelName: String): String {
        return "update tbl_${modelName} set sysDeleted = 1 where id = #{param1}"  //逻辑删除
    }

    fun getListByPid(modelName: String): String {
        var columns = columnMap[modelName]!!
        // return "select ${columnMap[modelName]!!.joinToString(",")} from tbl_${modelName} where ${columns[1]}=#{param1}"
        return "select * from tbl_${modelName} where ${columns[1]}=#{param1} and sysDeleted = 0"
    }

//    fun insertList(modelName: String): String {//新增choose 就不需要insertList的时候判断是否为空 这个是多个insert
//        var columns = columnMap[modelName]!!
//        var selectColumn = columns.joinToString(",")
//        var insertListValue = columns.map { "#{item.${it},jdbcType=VARCHAR}" }.joinToString(",")
//        return """<script><choose><when test="list.size()>0"><foreach collection ="list" item="item" separator =";">insert into tbl_${modelName} (${selectColumn}) values (${insertListValue})</foreach></when ><otherwise>select 1</otherwise></choose></script>"""
//    }

    fun insertList(modelName: String): String {//新增choose 就不需要insertList的时候判断是否为空 这个只有一个insert 效率高很多
        var columns = columnMap[modelName]!!
        var selectColumn = columns.joinToString(",")
        var insertListValue = columns.map { "#{item.${it},jdbcType=VARCHAR}" }.joinToString(",")
        return """<script><choose><when test="list.size()>0">insert into tbl_${modelName}  (${selectColumn}) values <foreach collection ="list" item="item" separator =",">(${insertListValue})</foreach></when ><otherwise>select 1</otherwise></choose></script>"""
    }

    fun insertShardList(modelName: String): String {//分表的插入
        var columns = columnMap[modelName]!!
        var selectColumn = columns.joinToString(",")
        var insertListValue = columns.map { "#{item.${it},jdbcType=VARCHAR}" }.joinToString(",")
        return """<script><choose><when test="list.size()>0"><foreach collection ="list" item="item" separator =";">insert into tbl_${modelName}${'$'}{item.sharding} (${selectColumn}) values (${insertListValue})</foreach></when ><otherwise>select 1</otherwise></choose></script>"""
    }
}
