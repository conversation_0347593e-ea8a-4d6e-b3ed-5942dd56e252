package com.shenlan.cnais.ais.client.model

import com.shenlan.cnais.ais.DataFlag
import com.shenlan.cnais.ais.RelatedProperty
import com.shenlan.cnais.ais.WebClientResponse
import com.shenlan.cnais.util.dateTimeFormatter
import com.shenlan.cnais.util.timestampToLocalDateTime

@WebClientResponse("Guangxi")
class GuangxiResponse {
    var code: Int = 0
    var message: String = ""
    @DataFlag var data: GuangxiPageData = GuangxiPageData()
}

class GuangxiPageData {
    var current: Int = 0
    var pages: Int = 0
    var size: Int = 0
    var total: Int = 0
    @DataFlag(true) var data: MutableList<GuangxiData> = mutableListOf()
}

/**
 * class BDShipData {
 *     // 静态数据
 *     var id: String = ""
 *     var card: String = ""
 *     var time: String = ""
 *     var mmsi: String = ""
 *     var eid: String = ""
 *     var imo: String = ""
 *     var callSign: String = ""
 *     var name: String = ""
 *     var type: String = ""
 *     var loa: String = ""
 *     var bm: String = ""
 *     var eta: String = ""
 *     var draught: String = ""
 *     var dest: String = ""
 *     var epfs: String = ""
 *     var mid: String = ""
 *     var umc: String = ""
 *     var sn: String = ""
 *     var fromType: String = ""
 *
 *     // 动态数据
 *     var state: String = ""
 *     var rot: String = ""
 *     var sog: String = ""
 *     var lng: String = ""
 *     var lat: String = ""
 *     var cog: String = ""
 *     var heading: String = ""
 *     var timestamp: String = ""
 * }
 */
class GuangxiData {
    // 静态信息
    @RelatedProperty("mmsi") var shipUuid: String = ""
    var ownerName: String = ""
    var distShipDistrictName: String = ""
    var nationalityCertNumber: String = ""
    var ownerHolderNo: String = ""
    var shipNetTon: String = ""
    var shipPort: String = ""
    var shipDeep: String = ""
    var shipWidth: String = ""
    var fishingPermitNumber: String = ""
    var shipCall: String = ""
    var registerNumber: String = ""
    var shipTotPower: String = ""
    var shipName: String = ""
    var shipNo: String = ""
    var normNumber: String = ""
    var mainCertificateNumber: String = ""
    var shipTotTon: String = ""
    var jobWay: String = ""
    var ownerTel: String = ""
    var shipType: String = ""
    var material: String = ""
    var ownerAddr: String = ""
    var shipLength: String = ""
    var shipBuildCompDate: String = ""

    // 动态信息
    @RelatedProperty("id") var posUuid: String = ""
    @RelatedProperty("mmsi") var termNo: String = ""
    @RelatedProperty("sog") var speed: String = ""
        get() = String.format("%.1f", (field.toDoubleOrNull() ?: 0.0) / 10.0)
    @RelatedProperty("lng") var longitude: String = ""
        get() = String.format("%.6f", (field.toDoubleOrNull() ?: 0.0) / 1000000.0)
    @RelatedProperty("lat") var latitude: String = ""
        get() = String.format("%.6f", (field.toDoubleOrNull() ?: 0.0) / 1000000.0)
    @RelatedProperty("name") var shipId: String = ""
    var azimuth: String = ""
    var termType: String = ""
    var rptTime: String = ""
    @RelatedProperty("time") var time: String = ""
        get() = dateTimeFormatter.format(timestampToLocalDateTime(rptTime.toLongOrNull() ?: 0L, false))
}

@WebClientResponse("Guangdong", true)
class GuangdongResponse {
    // 静态数据相关字段
    @RelatedProperty("loa") var CC: Int = 0
    @RelatedProperty("time") var CJSJ: String = ""                  // 对应 BDShipData.time (创建时间)
        get() = field.replace("/", "-")
    @RelatedProperty("bm") var CK: Int = 0
    @RelatedProperty("type") var CLX: String = ""                   // 对应 BDShipData.type (船舶类型)
    @RelatedProperty("name") var CM: String = ""  // 对应 BDShipData.name (船名)
    @RelatedProperty("heading") var CSX: Int = 0
    @RelatedProperty("state") var DHZT: String = ""  // 对应 BDShipData.state (航行状态)
    @RelatedProperty("eta") var ETA: String = ""     // 对应 BDShipData.eta (预计到达时间)
    @RelatedProperty("cog") var FX: Double = 0.0     // 对应 BDShipData.cog (航向)
    @RelatedProperty("callSign") var HH: String = "" // 对应 BDShipData.callSign (呼号)
    @RelatedProperty("id") var ID: String = ""       // 对应 BDShipData.id (ID)
    @RelatedProperty("imo") var IMO: String = ""     // 对应 BDShipData.imo (IMO号)
    @RelatedProperty("lng") var JD: Double = 0.0     // 对应 BDShipData.lng (经度)
    @RelatedProperty("dest") var MDD: String? = null // 对应 BDShipData.dest (目的地)
    @RelatedProperty("mmsi") var MMSI: Long = 0      // 对应 BDShipData.mmsi (MMSI)
    var MT: Int = 0
    @RelatedProperty("sog") var SD: Double = 0.0     // 对应 BDShipData.sog (速度)
    var TXZT: String = ""
    var TimeStamp: String = "" // 对应 BDShipData.timestamp (时间戳)
    @RelatedProperty("lat") var WD: Double = 0.0     // 对应 BDShipData.lat (纬度)
    var ZDCS: Int = 0
    var ZZD: String = ""
    var delay: Int = 0
}

class AuthResponse {
    var apiVersion: String = ""
    var code: String = ""
    var message: String = ""
    var requestId: String = ""
    var result: AuthResult = AuthResult()
}

class AuthResult {
    var authId: String = ""
    var authIp: String = ""
    var expiresIn: Int = 0
    var identityId: String = ""
    var refreshToken: String = ""
    var status: String = ""
    var token: String = ""
    var tokenCreateTime: Long = 0
    var username: String = ""
}

class HainanAuthResponse {
    var access_token: String = ""
    var token_type: String = ""
    var expires_in: Int = 0
    var scope: String = ""
}

class HainanAuthErrorResponse {
    var msg: String = ""
    var code: String = ""
    var token: String = ""
}

@WebClientResponse("Hainan")
class HainanResponse {
    @DataFlag(true) var result: MutableList<HainanResult> = mutableListOf()
    var total: Int = 0
    var returnSize: Int = 0
    var code: Int = 0
    var totalPages: Int = 0
    var message: String = ""
    var status: String = ""
}

/**
 * 序号	英文字段名	中文字段名	数据类型
 * 1	ID		String
 * 2	MMSI		String
 * 3	IMO		String
 * 4	CALLSIGN		String
 * 5	NAME		String
 * 6	TYPE		String
 * 7	LOA		String
 * 8	BM		String
 * 9	ETA		String
 * 10	DRAUGHT		String
 * 11	DEST		String
 * 12	EPFS		String
 * 13	MID		String
 * 14	UMC		String
 * 15	SN		String
 * 16	STATE		String
 * 17	ROT		String
 * 18	SOG		String
 * 19	LNG		String
 * 20	LAT		String
 * 21	COG		String
 * 22	HEADING		String
 * 23	TIMESTIMP		String
 * 24	FROMTYPE		String
 * 25	RCVTIME		String
 */
class HainanResult {
    @RelatedProperty("id") var ID: String = ""
    @RelatedProperty("mmsi") var MMSI: String = ""
    @RelatedProperty("imo") var IMO: String = ""
    @RelatedProperty("callSign") var CALLSIGN: String = ""
    @RelatedProperty("name") var NAME: String = ""
    @RelatedProperty("type") var TYPE: String = ""
    @RelatedProperty("loa") var LOA: String = ""
    @RelatedProperty("bm") var BM: String = ""
    @RelatedProperty("eta") var ETA: String = ""
    @RelatedProperty("draught") var DRAUGHT: String = ""
    @RelatedProperty("dest") var DEST: String = ""
    @RelatedProperty("epfs") var EPFS: String = ""
    @RelatedProperty("mid") var MID: String = ""
    @RelatedProperty("umc") var UMC: String = ""
    @RelatedProperty("sn") var SN: String = ""
    @RelatedProperty("state") var STATE: String = ""
    @RelatedProperty("rot") var ROT: String = ""
    @RelatedProperty("sog") var SOG: String = ""
    @RelatedProperty("lng") var LNG: String = ""
    @RelatedProperty("lat") var LAT: String = ""
    @RelatedProperty("cog") var COG: String = ""
    @RelatedProperty("heading") var HEADING: String = ""
    var TIMESTIMP: String = ""
//        get() = dateTimeFormatter.format(timestampToLocalDateTime(field.toLongOrNull() ?: 0L, false))
    @RelatedProperty("fromType") var FROMTYPE: String = ""
    @RelatedProperty("time") var RCVTIME: String = ""
}

class HeilongjiangResponse {
    var data: String? = null
    var time: Long = 0L
    var rtnMsg: String = ""
    var rtnCode: String = ""

    constructor()

    constructor(data: String?, time: Long, rtnMsg: String, rtnCode: String) {
        this.data = data
        this.time = time
        this.rtnMsg = rtnMsg
        this.rtnCode = rtnCode
    }
}