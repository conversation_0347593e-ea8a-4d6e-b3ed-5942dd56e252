package com.shenlan.cnais.ais.server

import com.shenlan.cnais.ais.Bytes
import com.shenlan.cnais.ais.Charset
import com.shenlan.cnais.ais.Order
import com.shenlan.cnais.ais.StructureFlag
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatDataStructure
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatManager
import kotlin.experimental.xor
import kotlin.reflect.KClass
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

object HexUtil {
    private val regex = Regex("(?:00)+$")  // 匹配结尾连续的00（偶数个）

    fun <T : Any> loadStructure(hexString: String, clazz: Class<T>): T {
        val structure = clazz.getDeclaredConstructor().newInstance()
        val properties = structure::class.memberProperties.filter {
            it.javaField?.getAnnotation(Order::class.java)?.order != null
        }.sortedBy {
            it.javaField?.getAnnotation(Order::class.java)?.order ?: 0
        }
        var offset = 0

        for (property in properties) {
            property.isAccessible = true
            if (property is KMutableProperty<*>) {
                val bytes = property.javaField?.getAnnotation(Bytes::class.java)?.bytes ?: 0
                val value = hexString.substring(offset, offset + bytes * 2)
//                println("Property: ${property.name}, Value: $value, Offset: $offset")
                offset += if (bytes == 0) {
                    property.setter.call(structure, hexString)
                    (property.getter.call(structure) as? String)?.length ?: 0
                } else {
                    when (property.returnType.classifier) {
                        String::class -> {
                            val charset = property.javaField?.getAnnotation(Charset::class.java)?.charset ?: ""
                            if (charset.isNotEmpty()) {
                                val strValue = hexToString(value, charset)
                                property.setter.call(structure, strValue)
                            } else {
                                property.setter.call(structure, value)
                            }
                        }
                        Int::class -> {
                            val intValue = value.toIntOrNull(16) ?: 0
                            property.setter.call(structure, intValue)
                        }
                        Long::class -> {
                            val longValue = value.toLongOrNull(16) ?: 0L
                            property.setter.call(structure, longValue)
                        }
                        else -> {
                            val propertyClazz = property.returnType.classifier as KClass<*>
                            if (propertyClazz.findAnnotation<StructureFlag>() != null) {
                                val subValue = loadStructure(value, propertyClazz.java)
                                property.setter.call(structure, subValue)
                            }
                        }
                    }
                    bytes * 2
                }
            }
        }

        return structure
    }

    fun xorEncrypt(structure: OceanBoatDataStructure, oceanBoatManager: OceanBoatManager) {
        var key = structure.messageHeader.encryptKey
        if (key == 0) key = 1

        key = oceanBoatManager.IA1 * (key % oceanBoatManager.M1) + oceanBoatManager.IC1

        // buffer[idx++] ^= (unsigned char)((key>>20)&0xFF);
        structure.messageBody = structure.messageBody.chunked(2).joinToString("") {
            String.format("%02X", it.toInt(16).toByte() xor ((key shr 20 and 0xFF).toByte()))
        }
    }

    fun hexToString(hex: String, charsetName: String): String {
        val cleanedHex = hex.replace(regex, "")
        val bytes = ByteArray(cleanedHex.length / 2) { i ->
            cleanedHex.substring(i * 2, i * 2 + 2).toInt(16).toByte()
        }
        return String(bytes, charset(charsetName))
    }
}