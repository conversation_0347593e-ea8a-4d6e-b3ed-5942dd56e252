package com.shenlan.cnais

import com.shenlan.cnais.ais.StationManageUtil
import com.shenlan.cnais.auto.CommercialVesselMapper
import com.shenlan.cnais.config.OpenMinaServer
import com.shenlan.cnais.task.StationMonitorTask
import com.shenlan.cnais.task.WebClientTask
import com.shenlan.cnais.util.*
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.web.servlet.ServletComponentScan
import org.springframework.context.ConfigurableApplicationContext
import org.springframework.context.annotation.Configuration
import org.springframework.jms.annotation.EnableJms
import org.springframework.scheduling.annotation.EnableScheduling
import java.net.InetAddress
import java.util.*
import java.util.concurrent.TimeUnit


object DefaultProfileUtil {
    fun addDefaultProfile(app: SpringApplication) {
        val defProperties = HashMap<String, Any>()
        defProperties["spring.profiles.default"] = "dev"
        app.setDefaultProperties(defProperties)
    }
}

@SpringBootApplication
@Configuration
@ServletComponentScan
@EnableScheduling
@EnableJms
open class Application {
    companion object {
        var context: ConfigurableApplicationContext? = null
    }
}

fun main(args: Array<String>) {
    try {
        Sql.init()
        val app = SpringApplication(Application::class.java)
        DefaultProfileUtil.addDefaultProfile(app)

        val applicationContext = app.run("")
        Application.context = applicationContext
        val env = applicationContext.environment
        val protocol = "http"
        "".log.info("\n  ----------------------------------------------------------\n\t" +
                "Application '{}' is running! Access URLs:\n\t" +
                "Local: \t\t{}://localhost:{}\n\t" +
                "External: \t{}://{}:{}\n\t" +
                "Profile(s): \t{}\n----------------------------------------------------------",
            env.getProperty("spring.application.name"),
            protocol,
            env.getProperty("server.port"),
            protocol,
            InetAddress.getLocalHost().hostAddress,
            env.getProperty("server.port"),
            env.activeProfiles)
        init()
    } catch (e: Throwable) {
        e.printStackTrace()
    }
}

fun init() {
    initMapper()
    StationManageUtil.init()
    OpenMinaServer.init()
    WebClientTask.init()
    StationMonitorTask.init()
}
