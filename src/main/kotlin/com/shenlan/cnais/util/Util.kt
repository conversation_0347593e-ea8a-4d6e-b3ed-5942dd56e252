package com.shenlan.cnais.util

import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.shenlan.BaseModel
import com.shenlan.cnais.auto.Dict
import com.shenlan.cnais.config.*
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationContextAware
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import java.io.*
import java.math.BigDecimal
import java.net.Inet4Address
import java.net.InetAddress
import java.net.NetworkInterface
import java.nio.charset.Charset
import java.security.MessageDigest
import java.security.Security
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import kotlin.math.asin
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.reflect.full.functions


fun time(action: () -> Unit) {
    var a = System.currentTimeMillis()
    action()
    println((System.currentTimeMillis() - a).toString() + "ms")
}

//fun methodlog(action: () -> Unit, name: String = "") {
//    "".log.info("begin method:${name}")
//    action()
//    "".log.info("end method:${name}")
//}
val customObjectMapper = CustomObjectMapper()

val jacksonObjectMapper = jacksonObjectMapper()

val jacksonObjectMapperIgnoreCase = jacksonObjectMapper().apply {
    // 启用忽略大小写
    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
}

val Any.log: Logger
    get() = LoggerFactory.getLogger(this.javaClass)

val Any.toJsonString: String
    get() = customObjectMapper.writeValueAsString(this)

fun <T> String?.toObject(cla: Class<T>): T = customObjectMapper.readValue(this.toString(), cla)

fun Any?.println(): Unit {
    println(this)
}

fun Any?.pj(): Unit {
    if (this == null) return
    println(this.toJsonString)
}

fun Any?.fpj(): Unit {
    if (this == null) return
    customObjectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(this).println()
}

fun Boolean.toChar(): String {
    return if (this == true) "1" else "0"
}

fun String?.notEmpty(): Boolean {
    return this != null && this.length != 0
}

fun String?.isEmpty(): Boolean {
    return this == null || this.length == 0
}

fun String?.firstLower(): String {
    if (this != null) {
        return this[0].toLowerCase() + this.substring(1..this.length - 1)
    }
    return ""
}

fun String?.firstUpper(): String {
    if (this != null) {
        return this[0].toUpperCase() + this.substring(1..this.length - 1)
    }
    return ""
}

//返回sql 中的 in 格式串
fun List<String>?.getInStr(): String {
    if (this == null || this.size == 0) {
        return ""
    }
    return "'" + this.joinToString("','") + "'"
}

//fun Collection<Any>?.isNullOrEmpty(): Boolean {
//    return this == null || this.isEmpty()
//}
val BaseModel.tableName: String
    get() = "TBL_" + this::class.simpleName?.toUpperCase()

fun <T> List<T>?.notempty(): Boolean {
    if (this == null) return false
    if (this.isEmpty()) return false
    return true
}

fun uuid(): String {
    return UUID.randomUUID().toString().replace("-", "")
}

fun errorlog(e: Throwable) {
    var trace = StringWriter()
    e.printStackTrace(PrintWriter(trace))
    "".log.error(trace.toString())
}

fun <T> getBean(requiredType: Class<T>): T {
    try {
        return SpringContext.context.getBean(requiredType)
    } catch (e: Exception) {
        if (requiredType.name.contains("Mapper")) {
            return MybatisUtil.sqlSession.getMapper(requiredType)
        } else {
            throw  Exception()
        }
    }
}

fun getBean(requiredType: String) = SpringContext.context.getBean(requiredType)

@Service
class SpringContext : ApplicationContextAware {
    override fun setApplicationContext(applicationContext: ApplicationContext) {
        context = applicationContext
    }

    companion object {
        lateinit var context: ApplicationContext
    }
}


/**
 * 只需传递对象 即可调用 该对象的Service的Save
 */
fun _save(any: Any): Any? {
    val bean = getBean(any::class.simpleName!!.toLowerCase() + "Service")
    return bean::class.functions.first { it.name == "save" }.call(bean, any)
}

//根据对象类型 调用Mapper的Insert
fun _insert(any: Any): Any? {
    val bean = getBean(Class.forName(any::class.java.canonicalName + "Mapper"))
    return bean::class.functions.first { it.name == "insert" }.call(bean, any)
}
fun _insertList(list: List<Any>) {
    val bean = getBean(Class.forName(list[0]::class.java.canonicalName + "Mapper"))
    bean::class.functions.first { it.name == "insertList" }.call(bean, list)
}
fun getUser(): User {
    var auth = SecurityContextHolder.getContext().authentication
    if (auth != null && auth.principal is AppUser) {
        return (auth.principal as AppUser).user
    }
//    if (AppPro.isDev()) return getDevUser()
    return AppUser("-", "-").user
}

var devuser: User? = null
fun getDevUser(): User {
    println("getDevUser")
    if (devuser == null) {
        println("----")
        var user = getUserByMobile(User().extraMobile)
        devuser = user
    }
    return devuser!!
}

val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
val simpleDateFormat2 = SimpleDateFormat("yyyy-MM-dd")
val etaFormat = SimpleDateFormat("MMddHHmm")
fun strToDate(str: String) = simpleDateFormat.parse(str)
fun intToDate(date: Int) = Date(date * 1000L)
fun intToStr(date: Int) = dateFormat(intToDate(date))
fun dateFormat(date: Date) = simpleDateFormat.format(date)
fun dateFormat2(date: Date) = simpleDateFormat2.format(date)
fun dateToInt(date: Date) = (date.time / 1000L).toInt()
fun getUtcSecond() = System.currentTimeMillis() / 1000
//保留i小数
fun Double.decimal(i: Int = 1) = String.format("%.${i}f", this).toDouble()

fun nDecimals(d: Double, n: Int = 2): Double {
    return BigDecimal(d).setScale(n, BigDecimal.ROUND_HALF_UP).toDouble()
}

fun String?.getItemName(dictCode: String): String {
    if (this == null || this == "") return ""
    return Dict.dictNameMap[dictCode + ";" + this] ?: ""
}

fun deepClone(obj: Any): Any? {
    var cloneObject: Any? = null;
    try {
        var outputStream: ByteArrayOutputStream = ByteArrayOutputStream();
        var objectOutputStream: ObjectOutputStream = ObjectOutputStream(outputStream);
        objectOutputStream.writeObject(obj);
        objectOutputStream.close();

        var inputStream: ByteArrayInputStream = ByteArrayInputStream(outputStream.toByteArray());
        var objectInputStream: ObjectInputStream = ObjectInputStream(inputStream);
        cloneObject = objectInputStream.readObject();
        objectInputStream.close();
    } catch (e: ClassNotFoundException) {
        e.printStackTrace();
    } catch (e: IOException) {
        e.printStackTrace();
    }
    return cloneObject;
}

fun getIp(): MutableList<String> {
    var ipList = mutableListOf<String>()
    val networkInterfaces = NetworkInterface.getNetworkInterfaces()
    var networkInterface: NetworkInterface
    var inetAddresses: Enumeration<InetAddress>
    var inetAddress: InetAddress?
    var ip: String
    while (networkInterfaces.hasMoreElements()) {
        networkInterface = networkInterfaces.nextElement()
        inetAddresses = networkInterface.getInetAddresses()
        while (inetAddresses.hasMoreElements()) {
            inetAddress = inetAddresses.nextElement()
            if (inetAddress != null && inetAddress is Inet4Address) { // IPV4
                ip = inetAddress.hostAddress
                ipList.add(ip)
            }
        }
    }
    return ipList
}
fun getShipTypeCode(code: String): String {
    if (code.startsWith("7")) return "1" //货船 70-79
    if (code.startsWith("8")) return "2" //油轮 80-89
    if (code.startsWith("6")) return "3" //客船 60-69
    if (code.startsWith("4")) return "4" //高速船 40-49
    if (code == "30") return "5" //渔船 30
    if (code == "37" || code == "36") return "6" //娱乐船 36-37
    //拖船/特种船 20-29、31-34、50-59
    if ("31,32,33,34".split(",").contains(code)||code.startsWith("5")||code.startsWith("2")) return "7"
    //0 35 38-39 90-99
    return "8"
}
object AesUtil {
    val PARAMS = "params"
    // 算法
    private val ALGORITHMSTR = "AES/ECB/PKCS5Padding"
    // 密钥
    private val AES_KEY = "bWFaeHB9ZA==WNST"

    /**
     * base 64 encode
     *
     * @param bytes 待编码的byte[]
     * @return 编码后的base 64 code
     */
    fun base64Encode(bytes: ByteArray): String {
        return Base64.getEncoder().encodeToString(bytes)
    }

    /**
     * base 64 decode
     *
     * @param base64Code 待解码的base 64 code
     * @return 解码后的byte[]
     * @throws Exception
     */
    fun base64Decode(base64Code: String): ByteArray? {
        return if (base64Code.isEmpty()) null else Base64.getDecoder().decode(base64Code)
    }

    /**
     * aes 解密
     *
     * @param encryptStr
     * @return
     */
    fun aesDecrypt(encryptStr: String): String? {
        return aesDecrypt(encryptStr, AES_KEY)
    }

    /**
     * aes 加密
     *
     * @param content
     * @return
     */
    fun aesEncrypt(content: String): String {
        if (AppPro.aes) {
            return aesEncrypt(content, AES_KEY)
        } else {
            return content
        }
    }

    /**
     * AES加密
     *
     * @param content    待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的byte[]
     * @throws Exception
     */
    @Throws(Exception::class)
    private fun aesEncryptToBytes(content: String, encryptKey: String): ByteArray {
        val kgen = KeyGenerator.getInstance("AES")
        kgen.init(128)
        val cipher = Cipher.getInstance(ALGORITHMSTR)
        cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(encryptKey.toByteArray(), "AES"))
        return cipher.doFinal(content.toByteArray(charset("utf-8")))
    }

    /**
     * AES加密为base 64 code
     *
     * @param content    待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的base 64 code
     * @throws Exception
     */
    private fun aesEncrypt(content: String, encryptKey: String): String {
        try {
            return base64Encode(aesEncryptToBytes(content, encryptKey))
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }

    }

    /**
     * AES解密
     *
     * @param encryptBytes 待解密的byte[]
     * @param decryptKey   解密密钥
     * @return 解密后的String
     * @throws Exception
     */
    @Throws(Exception::class)
    private fun aesDecryptByBytes(encryptBytes: ByteArray?, decryptKey: String): String {
        val kgen = KeyGenerator.getInstance("AES")
        kgen.init(128)

        val cipher = Cipher.getInstance(ALGORITHMSTR)
        cipher.init(Cipher.DECRYPT_MODE, SecretKeySpec(decryptKey.toByteArray(), "AES"))
        val decryptBytes = cipher.doFinal(encryptBytes!!)
        return String(decryptBytes, Charset.forName("utf-8"))
    }

    /**
     * 将base 64 code AES解密
     *
     * @param encryptStr 待解密的base 64 code
     * @param decryptKey 解密密钥
     * @return 解密后的string
     * @throws Exception
     */
    private fun aesDecrypt(encryptStr: String, decryptKey: String): String? {
        try {
            return if (encryptStr.isEmpty()) null else aesDecryptByBytes(base64Decode(encryptStr), decryptKey)
        } catch (e: Exception) {
            return encryptStr
        }

    }

}


// 地球的半径（单位：米）
const val EARTH_RADIUS = 6371000.0

// 角度转弧度
fun toRadians(degrees: Double): Double = degrees * (Math.PI / 180)

// 弧度转角度
fun toDegrees(radians: Double): Double = radians * (180 / Math.PI)

/**
 * 根据中心点的经纬度、半径、以及需要的顶点数量，返回多边形的顶点经纬度列表
 * @param lat 中心点的纬度
 * @param lon 中心点的经度
 * @param radius 半径（米）
 * @param numPoints 多边形的顶点数
 * @return 多边形的顶点经纬度列表
 */
fun generatePolygon(lat: Double, lon: Double, radius: Double, numPoints: Int): List<Pair<Double, Double>> {
    val polygonPoints = mutableListOf<Pair<Double, Double>>()

    // 把纬度和经度转换成弧度
    val latRad = toRadians(lat)
    val lonRad = toRadians(lon)

    // 每个顶点之间的角度间隔
    val angleStep = 360.0 / numPoints

    for (i in 0 until numPoints) {
        val angle = toRadians(i * angleStep)

        // 计算顶点的纬度（利用球面余弦定理）
        val newLat = asin(sin(latRad) * cos(radius / EARTH_RADIUS) + cos(latRad) * sin(radius / EARTH_RADIUS) * cos(angle))

        // 计算顶点的经度
        val newLon = lonRad + atan2(
            sin(angle) * sin(radius / EARTH_RADIUS) * cos(latRad),
            cos(radius / EARTH_RADIUS) - sin(latRad) * sin(newLat)
        )

        // 将顶点的纬度和经度从弧度转换回角度并存入列表
        polygonPoints.add(Pair(toDegrees(newLat), toDegrees(newLon)))
    }

    return polygonPoints
}

fun parseDMS(dms: String): Pair<Double, Double> {
    // 分割纬度和经度
    val parts = dms.split("-")
    val latPart = parts[0] // 纬度部分
    val lonPart = parts[1] // 经度部分

    // 解析纬度和经度
    val latitude = dmsToDecimal(latPart)
    val longitude = dmsToDecimal(lonPart)

    return Pair(latitude, longitude)
}

fun dmsToDecimal(dms: String): Double {
    // 先分割度分秒和方向信息
    val parts = dms.split("#")
    val dmsPart = parts[0].split(",")
    val direction = parts[1]

    // 获取度、分、秒
    val degrees = dmsPart[0].toDouble()
    val minutes = dmsPart[1].toDouble()
    val seconds = dmsPart[2].toDouble()

    // 将度分秒转换成十进制
    var decimal = degrees + (minutes / 60) + (seconds / 3600)

    // 根据方向（N, S, E, W）来调整符号
    if (direction == "S" || direction == "W") {
        decimal *= -1
    }

    return decimal
}

object SM4Util {
    init {
        // 添加 BouncyCastle 作为安全提供者
        Security.addProvider(BouncyCastleProvider())
    }

    private const val ALGORITHM_NAME = "SM4"
    private const val ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding"

    /**
     * SM4 ECB 模式加密
     * @param plaintext 明文
     * @param hexKey HEX格式的密钥
     * @return Base64编码的密文
     */
    fun encryptECB(plaintext: String, hexKey: String = "70533857533476714E6156634F673D3D"): String {
        val keyBytes = hexStringToByteArray(hexKey)
        val secretKey = SecretKeySpec(keyBytes, ALGORITHM_NAME)

        val cipher = Cipher.getInstance(ALGORITHM_NAME_ECB_PADDING, BouncyCastleProvider.PROVIDER_NAME)
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)

        val encrypted = cipher.doFinal(plaintext.toByteArray(Charsets.UTF_8))
        return Base64.getEncoder().encodeToString(encrypted)
    }

    /**
     * SM4 ECB 模式解密
     * @param ciphertext Base64编码的密文
     * @param hexKey HEX格式的密钥
     * @return 解密后的明文
     */
    fun decryptECB(ciphertext: String, hexKey: String = "70533857533476714E6156634F673D3D"): String {
        val keyBytes = hexStringToByteArray(hexKey)
        val secretKey = SecretKeySpec(keyBytes, ALGORITHM_NAME)

        val cipher = Cipher.getInstance(ALGORITHM_NAME_ECB_PADDING, BouncyCastleProvider.PROVIDER_NAME)
        cipher.init(Cipher.DECRYPT_MODE, secretKey)

        val decoded = Base64.getDecoder().decode(ciphertext)
        val decrypted = cipher.doFinal(decoded)
        return String(decrypted, Charsets.UTF_8)
    }

    /**
     * 16进制字符串转字节数组
     */
    private fun hexStringToByteArray(hex: String): ByteArray {
        require(hex.length % 2 == 0) { "Hex string must have even length" }

        return hex.chunked(2)
            .map { it.toInt(16).toByte() }
            .toByteArray()
    }

    /**
     * 字节数组转16进制字符串
     */
    private fun byteArrayToHexString(bytes: ByteArray): String {
        return bytes.joinToString("") { "%02x".format(it) }
    }
}

object Sm4CbcUtil {

    private const val ALGORITHM = "SM4"
    private const val MODE = "CBC"
    private const val PADDING = "PKCS7Padding"
    private const val TRANSFORMATION = "$ALGORITHM/$MODE/$PADDING"

    init {
        // 注册Bouncy Castle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(BouncyCastleProvider())
        }
    }

    /**
     * 将 ByteArray 转换为十六进制字符串
     */
    fun ByteArray.toHexString(): String =
        joinToString("") { byte -> "%02x".format(byte) }

    /**
     * 将十六进制字符串转换为 ByteArray
     */
    fun String.hexToByteArray(): ByteArray {
        require(length % 2 == 0) { "Hex string must have an even length" }
        return chunked(2)
            .map { it.toInt(16).toByte() }
            .toByteArray()
    }

    /**
     * SM4 CBC模式加密
     *
     * @param data 待加密数据
     * @param key SM4密钥 (16 bytes)
     * @param iv 初始化向量 (16 bytes)
     * @return 加密后的数据
     */
    fun encrypt(data: ByteArray, key: ByteArray, iv: ByteArray): ByteArray {
        check(key, iv)

        val secretKeySpec = SecretKeySpec(key, ALGORITHM)
        val ivParameterSpec = IvParameterSpec(iv)

        val cipher = Cipher.getInstance(TRANSFORMATION, BouncyCastleProvider.PROVIDER_NAME)
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec)

        return cipher.doFinal(data)
    }

    /**
     * SM4 CBC模式解密
     *
     * @param encryptedData 待解密数据
     * @param key SM4密钥 (16 bytes)
     * @param iv 初始化向量 (16 bytes)
     * @return 解密后的数据
     */
    fun decrypt(encryptedData: ByteArray, key: ByteArray, iv: ByteArray): ByteArray {
        check(key, iv)

        val secretKeySpec = SecretKeySpec(key, ALGORITHM)
        val ivParameterSpec = IvParameterSpec(iv)

        val cipher = Cipher.getInstance(TRANSFORMATION, BouncyCastleProvider.PROVIDER_NAME)
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec)

        return cipher.doFinal(encryptedData)
    }

    // 注册Bouncy Castle提供者
    fun addBouncyCastleProvider() {
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(BouncyCastleProvider())
        }
    }

    fun check(key: ByteArray, iv: ByteArray) {
        require(key.size == 16) { "Invalid key size. SM4 key must be 16 bytes." }
        require(iv.size == 16) { "Invalid IV size. SM4 IV must be 16 bytes." }
    }
}

object Md5Util {
    private val md5 = MessageDigest.getInstance("MD5")

    fun encrypt(input: String): ByteArray = md5.digest(input.toByteArray(Charsets.UTF_8))
}

val dateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
val localDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")
fun String?.toLocalDateTime(): LocalDateTime? {
    return try {
        this?.let { LocalDateTime.parse(it, dateTimeFormatter) }
    } catch (e: Exception) {
        println(e.message)
        null
    }
}

fun timestampToLocalDateTime(timestamp: Long, isMilliseconds: Boolean = true): LocalDateTime {
    // 如果时间戳是秒级（如 Unix 时间戳），则乘以 1000 转为毫秒
    val adjustedTimestamp = if (!isMilliseconds) timestamp * 1000 else timestamp
    return Instant.ofEpochMilli(adjustedTimestamp)
        .atZone(ZoneId.systemDefault()) // 使用系统默认时区
        .toLocalDateTime()
}

class FixedSizeQueue<T>(private val maxSize: Int) {
    private val deque = ArrayDeque<T>(maxSize)

    fun add(element: T) {
        if (deque.size >= maxSize) {
            deque.removeFirst()
        }
        if (element != null) {
            deque.addLast(element)
        }
    }

    fun getAll(): List<T> = deque.toList()

    fun size(): Int = deque.size

    fun clear() = deque.clear()

    fun peekFirst(): T? = deque.firstOrNull()

    fun peekLast(): T? = deque.lastOrNull()

    fun contains(element: T): Boolean = deque.toSet().contains(element)
}

fun hexStringToByteArray(hexString: String): ByteArray = hexString.split(Regex("\\s+"))
    .map { Integer.parseInt(it, 16).toByte() }
    .toByteArray()

fun ByteArray.toLittleEndianLong(): Long {
    var result = 0L
    this.forEachIndexed { index, b ->
        result = result or ((b.toLong() and 0xFF) shl (8 * index))
    }
    return result
}