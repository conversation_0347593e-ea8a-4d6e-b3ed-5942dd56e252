package com.shenlan.cnais.ais.server.oceanboat

object OceanBoatConstant {
    /**
     * 业务数据类型名称	业务数据类型标识	数值
     * 链路登录请求消息	UP_CONNECT_REQ	0x1001
     * 链路登录应答消息	UP_CONNECT_RSP	0x1002
     * 链路注销请求消息	UP_DISCONNECT_REQ	0x1003
     * 链路注销应答消息	UP_DISCONNECT_RSP	0x1004
     * 链路连接保持请求消息	UP_LINKTEST_REQ	0x1005
     * 链路连接保持应答消息	UP_LINKTEST_RSP	0x1006
     * 实时位置上传	UP_REAL_LOCATION	0x1101
     * 实时位置上传应答消息	UP_REAL_LOCATION_RSP	0x1102
     */
    const val UP_CONNECT_REQ = "1001"
    const val UP_CONNECT_RSP = "1002"
    const val UP_DISCONNECT_REQ = "1003"
    const val UP_DISCONNECT_RSP = "1004"
    const val UP_LINKTEST_REQ = "1005"
    const val UP_LINKTEST_RSP = "1006"
    const val UP_REAL_LOCATION = "1101"
    const val UP_REAL_LOCATION_RSP = "1102"

    const val GBK = "GBK"
    const val US_ASCII = "US-ASCII"
}