package com.shenlan.cnais.util

class BitReader(private val data: ByteArray) {
    private var byteIdx = 0      // 当前所处字节
    private var bitOffset = 0    // 0 = 高位，7 = 低位

    /** 读取 n (1‒64) 个比特，返回低 n 位有效的 Long */
    fun readBits(n: Int): Long {
//        require(n in 1..64) { "n must be 1..64" }

        var remaining = n
        var value = 0L

        while (remaining > 0) {
            val bitsLeftInByte = 8 - bitOffset
            val bitsToRead = minOf(remaining, bitsLeftInByte)

            val currentByte = data[byteIdx].toInt() and 0xFF
            val shift = bitsLeftInByte - bitsToRead
            val mask = (1 shl bitsToRead) - 1

            val bits = (currentByte shr shift) and mask
            value = (value shl bitsToRead) or bits.toLong()

            bitOffset += bitsToRead
            if (bitOffset == 8) {              // 下一个字节
                bitOffset = 0
                byteIdx++
            }
            remaining -= bitsToRead
        }
        return value
    }

    fun readBitsToByteArray(bitCount: Int): ByteArray {
        val byteCount = (bitCount + 7) / 8
        val result = ByteArray(byteCount)
        var bitsWritten = 0

        for (i in 0 until byteCount) {
            val bitsLeft = bitCount - bitsWritten
            val bitsThisByte = minOf(8, bitsLeft)
            val value = readBits(bitsThisByte).toInt() and ((1 shl bitsThisByte) - 1)
            result[i] = (value shl (8 - bitsThisByte)).toByte()  // 左对齐该字节
            bitsWritten += bitsThisByte
        }
        return result
    }
}