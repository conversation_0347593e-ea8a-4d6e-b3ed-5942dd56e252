package com.shenlan.cnais.ais.server.beidou

import com.shenlan.cnais.ais.Rate
import com.shenlan.cnais.ais.RawData
import com.shenlan.cnais.ais.RelatedProperty
import com.shenlan.cnais.ais.StationManageUtil
import com.shenlan.cnais.auto.Datapushuser
import com.shenlan.cnais.auto.DatapushuserMapper
import com.shenlan.cnais.util.*
import org.springframework.stereotype.Component
import java.security.MessageDigest
import java.util.*
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

@Component
object BDShipUtil {
    fun checkLogin(message: String): Datapushuser {
        return try {
            val loginRequest = jacksonObjectMapper.readValue(SM4Util.decryptECB(message), BDShipLoginRequest::class.java)
            if (loginRequest.username.isNotEmpty()) {
                val user = getBean(DatapushuserMapper::class.java).getInfoByUsername(loginRequest.username)
                if (verifyPassword(user.password, loginRequest.password)) {
                    user
                } else {
                    log.error("Invalid password for user: ${loginRequest.username}, password: ${loginRequest.password}")
                    Datapushuser()
                }
            } else
                Datapushuser()
        } catch (e: Exception) {
            log.error("Error parsing login request: message: $message, ${e.message}")
            Datapushuser()
        }
    }

    fun verifyPassword(inputPassword: String, storedHash: String): Boolean {
        val salted = "CNAC+$inputPassword"
        val md5 = MessageDigest.getInstance("MD5")
        val hash = md5.digest(salted.toByteArray()).joinToString("") { "%02x".format(it) }
        return hash.equals(storedHash, ignoreCase = true)
    }


    fun deal(message: String, dataPushUser: Datapushuser) {
        val data = if (dataPushUser.dataType == "0") {
            jacksonObjectMapperIgnoreCase.readValue(SM4Util.decryptECB(message), BDShipData::class.java)
        } else
            jacksonObjectMapperIgnoreCase.readValue(message, BDShipData::class.java)

        val rawData = RawData().apply {
            hash = data.hashCode()
            fromType = data.fromType
            date = data.time
            bdShipData = data
        }
        StationManageUtil.parent?.offer(rawData)
    }

    fun copy(data: Any, clazz: Class<*>): Any {
        val instance = clazz.getDeclaredConstructor().newInstance()
        val originProperties = data::class.memberProperties

        instance::class.memberProperties.forEach { property ->
            originProperties.find { it.name == property.name }?.let { originProperty ->
                originProperty.isAccessible = true
                originProperty.getter.call(data)?.let { value ->
                    if (property is KMutableProperty<*>) {
                        property.isAccessible = true
                        when (property.returnType.classifier) {
                            String::class -> {
                                property.setter.call(instance, value.toString())
                            }
                            Int::class -> {
                                property.setter.call(instance, value.toString().toIntOrNull() ?: 0)
                            }
                            Long::class -> {
                                property.setter.call(instance, value.toString().toLongOrNull() ?: 0L)
                            }
                            Double::class -> {
                                val rate = property.javaField?.getAnnotation(Rate::class.java)?.rate ?: 1.0
                                property.setter.call(instance, (value.toString().toDoubleOrNull() ?: 0.0) * rate)
                            }
                            Float::class -> {
                                val rate = property.javaField?.getAnnotation(Rate::class.java)?.rate ?: 1.0
                                property.setter.call(instance, (value.toString().toFloatOrNull() ?: 0.0f) * rate.toFloat())
                            }
                        }
                    }
                }
            }
        }

        return instance
    }

    fun copyWithRelatedProperties(relatedData: Any): BDShipData {
        val bdShipData = BDShipData()
        val relatedProperties = relatedData::class.memberProperties.filter { it.javaField?.getAnnotation(RelatedProperty::class.java) != null }
        val bdShipDataProperties = bdShipData::class.memberProperties

        relatedProperties.forEach { relatedProperty ->
            val relatedPropertyName = relatedProperty.javaField?.getAnnotation(RelatedProperty::class.java)?.propertyName
            bdShipDataProperties.find {  it.name == relatedPropertyName }?.let { bdShipDataProperty ->
                if (bdShipDataProperty is KMutableProperty<*>) {
                    relatedProperty.isAccessible = true
                    bdShipDataProperty.isAccessible = true
                    val relatedValue = relatedProperty.getter.call(relatedData)
                    if (bdShipDataProperty.returnType.classifier == String::class) {
                        val rate = bdShipDataProperty.javaField?.getAnnotation(Rate::class.java)?.rate
                        if (rate == null) {
                            bdShipDataProperty.setter.call(bdShipData, relatedValue.toString())
                        } else {
                            bdShipDataProperty.setter.call(bdShipData, ((relatedValue.toString().toDoubleOrNull() ?: 0.0) / rate).toString())
                        }
                    }
                }
            }
        }

        return bdShipData
    }
}