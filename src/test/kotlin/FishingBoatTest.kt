import com.shenlan.cnais.ais.Bytes
import com.shenlan.cnais.ais.server.HexUtil
import com.shenlan.cnais.ais.Order
import com.shenlan.cnais.ais.StructureFlag
import com.shenlan.cnais.ais.server.oceanboat.*
import com.shenlan.cnais.util.BitReader
import com.shenlan.cnais.util.hexStringToByteArray
import com.shenlan.cnais.util.println
import com.shenlan.cnais.util.toJsonString
import com.shenlan.cnais.util.toLittleEndianLong
import org.junit.Test
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.charset.Charset
import kotlin.reflect.KClass
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

class FishingBoatTest {
    @Test
    fun test1() {
        val hexString = """
            EB 90 8B 00 01 04 02 00 00 00 00 01 CE BA 95 18 00 CD CC CC 3F
            7E F3 F5 8F 98 91 5C 40 25 B7 13 12 56 90 36 40 00 40 9C 43 15
            00 00 00 00 00 40 40 40 40 40 40 40 00 59 41 4E 20 47 41 4E 47
            20 54 55 4F 20 37 20 20 20 20 20 20 00 00 00 00 00 00 00 00 00
            00 00 00 00 00 00 10 00 13 00 03 07 34 06 11 09 1E 33 33 73 40
            48 49 55 20 5A 48 4F 55 20 20 20 20 20 20 20 20 20 20 20 20 00
            00 00 00 00 00 00 01 00 00 00 00 00 00
        """.trimIndent()

        val bytes = hexStringToByteArray(hexString)
        println(bytes.size)
        val reader = BitReader(bytes)

        println(reader.readBits(8))
        println(reader.readBits(8))
        println(reader.readBits(16))
        println(reader.readBits(8))
        println(reader.readBits(16))
        println(reader.readBits(16))
        println(reader.readBits(16))
        reader.readBits(8)
        println(reader.readBitsToByteArray(32).toLittleEndianLong())
    }

    @Test
    fun test2() {
        val properties = OceanBoatDataStructure::class.memberProperties.filter {
            it.javaField?.getAnnotation(Order::class.java)?.order != null
        }.sortedBy {
            it.javaField?.getAnnotation(Order::class.java)?.order ?: 0
        }

        properties.forEach {
            println("${it.name}: ${(it.returnType.classifier as? KClass<*>)?.findAnnotation<StructureFlag>()}")
        }
    }

    @Test
    fun test3() {
        val structure = OceanBoatDataStructure().apply {
            headFlag = "5b"
            messageHeader = MessageHeader().apply {
                msgLength = 126
                msgSn = 0
                msgType = "1101"
                msgGNSSCenterId = "00000000"
                versionFlag = "000000"
                encryptFlag = 0
                encryptKey = 0
            }
            crcCode = "0000"
            endFlag = "5d"
            body = UpRealLocation().apply {
                msgId = 412114514
                shipName = "00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000".replace(" ", "")
                terminalNo = "00000000 00000000 00000000 000000".replace(" ", "")
                terminalType = 0
                posType = 0
                utc = 1746603800
                lon = 1221000
                lat = 223000
                course = 1145
                trueHeading = 1919
                speed = 19
                status = 0
                flag = 0
            }
            messageBody = (body as UpRealLocation).toHex()
        }

        val hex = structure.toHex()
        println(hex)
        val manager = OceanBoatManager()
        println(manager.deal(hex, null).toJsonString)
    }

    fun Any.toHex(): String {
        val properties = this::class.memberProperties.filter {
            it.javaField?.getAnnotation(Order::class.java)?.order != null
        }.sortedBy {
            it.javaField?.getAnnotation(Order::class.java)?.order ?: 0
        }
        var str = ""

        properties.forEach { property ->
            property.isAccessible = true
            val bytes = property.javaField?.getAnnotation(Bytes::class.java)?.bytes ?: 0
            val value = property.getter.call(this)
            if (value is String) {
                str += value
//                println("Property: ${property.name}, Value: $value, Bytes: $bytes")
            } else if (value is Int) {
                str += value.toString(16).padStart(bytes * 2, '0')
//                println("Property: ${property.name}, Value: ${value.toString(16).padStart(bytes * 2, '0')}, Bytes: $bytes")
            } else if (value is Long) {
                str += value.toString(16).padStart(bytes * 2, '0')
//                println("Property: ${property.name}, Value: ${value.toString(16).padStart(bytes * 2, '0')}, Bytes: $bytes")
            } else if (value is MessageHeader) {
                str += value.toHex()
//                println("Property: ${property.name}, Value: ${value.toHex()}, Bytes: $bytes")
            }
        }
        return str
    }

    fun stringToHexAscii(input: String): String {
        return input.toByteArray(Charsets.US_ASCII).joinToString("") { "%02X".format(it) }
    }

    fun stringToHexGBK(input: String): String {
        return input.toByteArray(Charset.forName("GBK")).joinToString("") { "%02X".format(it) }
    }

    fun hexToString(hex: String, charsetName: String): String {
        val bytes = ByteArray(hex.length / 2) { i ->
            hex.substring(i * 2, i * 2 + 2).toInt(16).toByte()
        }
        return String(bytes, charset(charsetName))
    }


    @Test
    fun test4() {
        val str = "你好ABC"
        val ascii = stringToHexAscii(str).padEnd(20, '0')
        val gbk = stringToHexGBK(str).padEnd(20, '0')
        println(ascii)
        println(gbk)

        val asciiStr = HexUtil.hexToString(ascii, "US-ASCII")
        val gbkStr = HexUtil.hexToString(gbk, "GBK")
        println(asciiStr)
        println(gbkStr)
    }
}