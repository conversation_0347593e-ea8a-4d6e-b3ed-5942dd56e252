package com.shenlan.cnais.ais.msa.anhui

import com.shenlan.cnais.ais.msa.anhui.AnhuiConstant.AIS_SHIP_DATA
import com.shenlan.cnais.ais.msa.anhui.AnhuiConstant.BASE_STATION_FAILURE_INFORMATION
import com.shenlan.cnais.ais.msa.anhui.AnhuiConstant.BASE_STATION_LOCATION_DATA
import com.shenlan.cnais.util.BitReader
import com.shenlan.cnais.util.toLittleEndianDouble
import com.shenlan.cnais.util.toLittleEndianLong
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 起始标志1	1	BYTE
 * 起始标志2	1	BYTE
 * 报文长度	2	UShort
 * 信息类型	1	BYTE
 * 数据源ID	2	UShort
 * 目标ID	2	UShort
 * 备用	2	UShort
 */
class AnhuiModel {
    var start1: Byte = 0x68
    var start2: Byte = 0x68
    var length: Short = 0
    var type: Byte = 0
    var sourceId: Short = 0
    var targetId: Short = 0
    var spare: Short = 0
    var payloadType: Int = 0
    var payload: Any? = null

    constructor()

    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        start1 = reader.readBits(8).toByte()
        start2 = reader.readBits(8).toByte()
        length = reader.readBitsToByteArray(16).toLittleEndianLong().toShort()
        type = reader.readBits(8).toByte()
        sourceId = reader.readBitsToByteArray(16).toLittleEndianLong().toShort()
        targetId = reader.readBitsToByteArray(16).toLittleEndianLong().toShort()
        spare = reader.readBits(16).toShort()
        payloadType = reader.readBits(8).toInt()
        // 拷贝byteArray剩余所有字节
        val remainingBytes = reader.readBitsToByteArray((length - 12) * 8)
        payload = when (payloadType) {
            AIS_SHIP_DATA -> AnhuiAisShipData(remainingBytes)
    //            BASE_STATION_LOCATION_DATA -> AnhuiBaseStationLocationData(remainingBytes)
    //            BASE_STATION_FAILURE_INFORMATION -> AnhuiBaseStationFailureInformation(remainingBytes)
            else -> remainingBytes
        }
    }
}

/**
 * 唯一识别码	4	Int
 * 航行状态	1	BYTE
 * 对地航速	4	float
 * 经度	8	double
 * 纬度	8	double
 * 对地航向	4	float
 * 艏向	2	short
 * IMO编号，	4	int
 * 呼号	8	Char数组
 * 名称	35	Char数组
 * 天线距离A	2	short
 * 天线距离B	2	short
 * 天线距离C	1	BYTE
 * 天线距离D	1	BYTE
 * 船货类型	1	BYTE
 * 预计到达时间的月	1	BYTE
 * 预计到达时间的日	1	BYTE
 * 预计到达时间的时	1	BYTE
 * 预计到达时间的分	1	BYTE
 * 最大吃水深度	4	float
 * 目的地	21	Char数组
 * AIS设备类型	1	BYTE
 * 船载人数	4	Int
 * 消息部分ID	1	BYTE
 * 当前消息ID	1	BYTE
 * 船舶更新次数	4	Int
 * 转向率	2	Short
 */
class AnhuiAisShipData {
    var mmsi: Int = 0
    var navigationStatusCode: Int = 0
    var speedOverGround: Double = 0.0
    var longitude: Double = 0.0
    var latitude: Double = 0.0
    var courseOverGround: Double = 0.0
    var heading: Int = 0
    var imo: Int = 0
    var callSign: String = ""
    var name: String = ""
    var dimensionA: Int = 0
    var dimensionB: Int = 0
    var dimensionC: Int = 0
    var dimensionD: Int = 0
    var shipType: Int = 0
    var etaMonth: Int = 0
    var etaDay: Int = 0
    var etaHour: Int = 0
    var etaMinute: Int = 0
    var draught: Double = 0.0
    var destination: String = ""
    var aisDeviceType: Int = 0
    var shipCapacity: Int = 0
    var messagePartId: Int = 0
    var currentMessageId: Int = 0
    var shipUpdateCount: Int = 0
    var rateOfTurn: Int = 0

    constructor()

    constructor(byteArray: ByteArray) {
        val reader = BitReader(byteArray)
        mmsi = reader.readBitsToByteArray(32).toLittleEndianLong().toInt()
        navigationStatusCode = reader.readBits(8).toInt()
        speedOverGround = reader.readBitsToByteArray(32).toLittleEndianDouble()
        longitude = reader.readBitsToByteArray(64).toLittleEndianDouble()
        latitude = reader.readBitsToByteArray(64).toLittleEndianDouble()
        courseOverGround = reader.readBitsToByteArray(32).toLittleEndianDouble()
        heading = reader.readBitsToByteArray(16).toLittleEndianLong().toInt()
        imo = reader.readBitsToByteArray(32).toLittleEndianLong().toInt()
        callSign = reader.readBitsToByteArray(64).toString(Charsets.UTF_8)
        name = reader.readBitsToByteArray(280).toString(Charsets.UTF_8)
        dimensionA = reader.readBitsToByteArray(16).toLittleEndianLong().toInt()
        dimensionB = reader.readBitsToByteArray(16).toLittleEndianLong().toInt()
        dimensionC = reader.readBits(8).toInt()
        dimensionD = reader.readBits(8).toInt()
        shipType = reader.readBits(8).toInt()
        etaMonth = reader.readBits(8).toInt()
        etaDay = reader.readBits(8).toInt()
        etaHour = reader.readBits(8).toInt()
        etaMinute = reader.readBits(8).toInt()
        draught = reader.readBitsToByteArray(32).toLittleEndianDouble()
        destination = reader.readBitsToByteArray(168).toString(Charsets.UTF_8)
        aisDeviceType = reader.readBits(8).toInt()
        shipCapacity = reader.readBitsToByteArray(32).toLittleEndianLong().toInt()
        messagePartId = reader.readBits(8).toInt()
        currentMessageId = reader.readBits(8).toInt()
        shipUpdateCount = reader.readBitsToByteArray(32).toLittleEndianLong().toInt()
        rateOfTurn = reader.readBitsToByteArray(16).toLittleEndianLong().toInt()
    }
}
