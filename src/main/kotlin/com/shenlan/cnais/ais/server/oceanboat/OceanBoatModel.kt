package com.shenlan.cnais.ais.server.oceanboat

import com.shenlan.cnais.ais.Bytes
import com.shenlan.cnais.ais.Charset
import com.shenlan.cnais.ais.Order
import com.shenlan.cnais.ais.StructureFlag
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.GBK
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.US_ASCII

/**
 * time_t	64位无符号整型，8字节
 * BYTE	单字节
 * BYTES	多字节
 * Octet String	定长字符串，采用ASCII编码，位数不足时，右补十六进制0x00。
 * String	定长字符串，采用GBK编码，位数不足时，右补十六进制0x00。
 * uint16_t	16位无符号整型，2字节
 * int16_t	16位有符号整型，2字节
 * uint32_t	32位无符号整型，4字节
 * int32_t	32位有符号整型，4字节
 * Key	序列号，在整个系统中是唯一的，规则：由系统接入代码(4位10进制数,系统分配）、UTC日期戳（14位10进制数,格式YYYYMMDDHH24MISS）和5位唯一的十进制序列号串联而成，如：11012009013011552311111。
 *
 * 说明：
 * 链路消息的MSG_ID参照上述Key定义，系统接入码统一填写0000
 */

/**
 * Head Flag	头标识
 * Message Header	数据头
 * Message Body	数据体
 * CRC Code	CRC校验码
 * End Flag	尾标识
 */
@StructureFlag(true)
class OceanBoatDataStructure {
    @Order(1) @Bytes(1) var headFlag: String = "5b"
    @Order(2) @Bytes(22) var messageHeader: MessageHeader = MessageHeader()
    @Order(3) @Bytes(0) var messageBody: String = ""
        set(value) {
            try {
                field = value.substring(46, messageHeader.msgLength * 2 - 6)
//                println(field)
            } catch (e: Exception) {
                e.printStackTrace()
                field = value
            }
        }
    @Order(4) @Bytes(2) var crcCode: String = ""
    @Order(5) @Bytes(1) var endFlag: String = "5d"
    var body: Any? = null
}

/**
 * MSG_LENGTH	uint32_t	数据长度（包括头标识、数据头、数据体、CRC校验码和尾标识）
 * MSG_SN	uint32_t	报文序列号
 * MSG_TYPE	uint16_t	业务数据类型
 * MSG_GNSSCENTERID	uint32_t	数据推送方接入码000000。
 * VERSION_FLAG	BYTES	协议版本号标识，数据推送方之间采用的标准协议版本编号；长度为3个字节来表示：0x01 0x02 0x0F 表示的版本号是V1.2.15，依此类推。当前版本号为1.0.0
 * ENCRYPT_FLAG	BYTE	报文加密标识位b：0 表示报文不加密，1 表示报文加密。
 * ENCRYPT_KEY	uint32_t	数据加密的密钥，长度为4个字节。
 * a占用四个字节，为发送信息的序列号，用于接收方检测是否有信息的丢失。数据接收方和数据推送方按自己发送数据包的个数计数，互不影响。程序开始运行时等于零，发送第一帧数据时开始计数，到最大数后自动归零。
 * b用来区分报文是否进行加密，如果标识为1，则说明对后续相应业务的数据体采用ENCRYPT_KEY对应的密钥进行加密处理。如果标识为0，则说明不进行加密处理。
 */
@StructureFlag(true)
class MessageHeader {
    @Order(1) @Bytes(4) var msgLength: Int = 0
    @Order(2) @Bytes(4) var msgSn: Int = 0
    @Order(3) @Bytes(2) var msgType: String = ""
    @Order(4) @Bytes(4) var msgGNSSCenterId: String = ""
    @Order(5) @Bytes(3) var versionFlag: String = ""
    @Order(6) @Bytes(1) var encryptFlag: Int = 0
    @Order(7) @Bytes(4) var encryptKey: Int = 0
}

/**
 * 字段名	字节数	类型	描述及要求
 * USERID	4	uint32_t	用户名
 * PASSWORD	8	Octet String	密码
 * DOWN_LINK_IP	32	Octet String	数据推送方提供对应的服务端IP地址
 * DOWN_LINK_PORT	2	uint16_t	数据推送方提供对应的服务端口号
 */
@StructureFlag(true)
class UpConnectReq {
    @Order(1) @Bytes(4) var userId: Int = 0
    @Order(2) @Bytes(8) var password: String = ""
    @Order(3) @Bytes(32) var downLinkIp: String = ""
    @Order(4) @Bytes(2) var downLinkPort: Int = 0
}

/**
 * 字段名	字节数	类型	描述及要求
 * USERID	4	uint32_t	用户名
 * PASSWORD	8	Octet String	密码
 */
@StructureFlag(true)
class UpDisconnectReq {
    @Order(1) @Bytes(4) var userId: Int = 0
    @Order(2) @Bytes(8) var password: String = ""
}

/**
 * 字段名	字节数	类型	描述及要求
 * MSG_ID	23	Key	信息唯一编码
 * SHIP_NAME	32	String	船名
 * TERMINAL_NO	15	Octet String	终端号码
 * TERMINAL_TYPE	2	uint16_t	终端类型，参见表A.3
 * POS_TYPE	2	uint16_t	位置类型，参见表A.4
 * UTC	8	time_t	定位时间，参见表A.1
 * LON	4	uint32_t	经度，参见表A.1
 * LAT	4	uint32_t	纬度，参见表A.1
 * COURSE	2	uint16_t	航向，参见表A.1
 * TRUEHEADING	2	uint16_t	船艏向，参见表A.1
 * SPEED	2	uint16_t	速度，参见表A.1
 * STATUS	2	uint16_t	状态，参见表A.6
 * FLAG	2	uint16_t	0:实时数据
 */
@StructureFlag(true)
class UpRealLocation {
    @Order(1) @Bytes(23) var msgId: Long = 0L
    @Order(2) @Bytes(32) @Charset(GBK) var shipName: String = ""
    @Order(3) @Bytes(15) @Charset(US_ASCII) var terminalNo: String = ""
    @Order(4) @Bytes(2) var terminalType: Int = 0
    @Order(5) @Bytes(2) var posType: Int = 0
    @Order(6) @Bytes(8) var utc: Long = 0
    @Order(7) @Bytes(4) var lon: Long = 0
    @Order(8) @Bytes(4) var lat: Long = 0
    @Order(9) @Bytes(2) var course: Int = 0
    @Order(10) @Bytes(2) var trueHeading: Int = 0
    @Order(11) @Bytes(2) var speed: Int = 0
    @Order(12) @Bytes(2) var status: Int = 0
    @Order(13) @Bytes(2) var flag: Int = 0
}

/**
 * 序号	字段名称	类型	长度	字段说明
 * 1	SHIP_DISTRICT_NAME	String	64	渔船属地
 * 2	MMSI	String	64	九位码
 * 3	SHIP_NAME	String	64	船名
 * 4	SHIP_TYPE	Int	4	船舶类型，
 * 渔船	0
 * 执法渔政船	1
 * 渔业辅助船	2
 * 远洋渔船	3
 * 其他	99
 * 5	WORK_TYPE	String	128	作业类型
 * 6	WORK_METHOD	String	128	作业方式
 * 7	LENGTH	String	64	船长
 * 8	WIDTH	String	64	型宽
 * 9	DEEP	String	64	型深
 * 10	TOT_TON	String	64	总吨位
 * 11	TOT_POWER	String	64	主机功率
 */
class ShipInfo {
    var SHIP_DISTRICT_NAME: String = ""
    var MMSI: String = ""
    var SHIP_NAME: String = ""
    var SHIP_TYPE: Int = 0
    var WORK_TYPE: String = ""
    var WORK_METHOD: String = ""
    var LENGTH: String = ""
    var WIDTH: String = ""
    var DEEP: String = ""
    var TOT_TON: String = ""
    var TOT_POWER: String = ""
}