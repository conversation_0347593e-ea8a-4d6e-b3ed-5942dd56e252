package com.shenlan.cnais.util

class BitWriter(maxSize: Int) {
    private var bitOffset = 0
    private var currentByte = 0
    private var writeIndex = 0
    private var buffer = ByteArray(maxSize)

    fun writeBits(inputBits: Int, bitCount: Int = 6) {
        var remainingBits = bitCount
        var bitsToWrite = inputBits

        while (remainingBits > 0) {
            val spaceInCurrentByte = 8 - bitOffset

            if (remainingBits >= spaceInCurrentByte) {
                val result = (currentByte or (bitsToWrite shr (remainingBits - spaceInCurrentByte))).toByte()
                buffer[writeIndex++] = result

                remainingBits -= spaceInCurrentByte
                bitsToWrite = bitsToWrite and ((1 shl remainingBits) - 1)
                bitOffset = 0
                currentByte = 0
            } else {
                currentByte = currentByte or (bitsToWrite shl (spaceInCurrentByte - remainingBits))

                bitOffset += remainingBits
                remainingBits = 0
            }
        }
    }

    /**
     * 获取当前写入的所有字节。
     * 如果最后一个字节未满 8 比特，剩余部分将用 0 填充。
     */
    fun toByteArray(): ByteArray {
        // 如果 currentByte 中还有未写入的比特，将其写入最后一个字节
        if (bitOffset > 0) {
            ensureCapacity(writeIndex + 1)
            buffer[writeIndex++] = currentByte.toByte()
        }
        return buffer.copyOf(writeIndex)
    }

    fun reset() {
        bitOffset = 0
        currentByte = 0
        writeIndex = 0
    }

    /**
     * 确保内部缓冲数组有足够的容量。
     */
    private fun ensureCapacity(minCapacity: Int) {
        if (minCapacity > buffer.size) {
            val newCapacity = (buffer.size * 2).coerceAtLeast(minCapacity)
            buffer = buffer.copyOf(newCapacity)
        }
    }
}