package com.shenlan.cnais.ais.client

import com.shenlan.cnais.ais.*
import com.shenlan.cnais.ais.client.WebClientUtil.getFromTypeByUniqueId
import com.shenlan.cnais.ais.client.WebClientUtil.responseClassesMap
import com.shenlan.cnais.ais.server.beidou.BDShipUtil.copyWithRelatedProperties
import com.shenlan.cnais.ais.server.oceanboat.UpRealLocation
import com.shenlan.cnais.util.FixedSizeQueue
import com.shenlan.cnais.util.dateFormat
import com.shenlan.cnais.util.intToStr
import com.shenlan.cnais.util.jacksonObjectMapper
import com.shenlan.cnais.util.toJsonString
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.WebClient
import java.util.*
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

class ApiRequestConfig {
    var endpoint: String = ""
    var params = mutableMapOf<String, String>()
    var type: String = "get"

    constructor(endpoint: String, params: MutableMap<String, String> = mutableMapOf()) {
        this.endpoint = endpoint
        this.params = params
    }
}

class WebClientManager {
    var webClient: WebClient? = null
    var responseUniqueId: String = ""
    var apiRequestConfigList = mutableListOf<ApiRequestConfig>()
    var repeatQueue = FixedSizeQueue<Int>(20000)
    var authResponse: Any? = null
    var params = mutableMapOf<String, String>()
    var loading = false

    constructor()

    constructor(url: String, uniqueId: String) {
        webClient = WebClient.builder()
            .baseUrl(url)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build()
        responseUniqueId = uniqueId
    }
}