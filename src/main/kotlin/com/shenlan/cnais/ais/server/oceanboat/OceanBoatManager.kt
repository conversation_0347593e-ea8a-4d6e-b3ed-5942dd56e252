package com.shenlan.cnais.ais.server.oceanboat

import com.shenlan.cnais.ais.RawData
import com.shenlan.cnais.ais.StationManageUtil
import com.shenlan.cnais.ais.server.BaseManager
import com.shenlan.cnais.ais.server.HexUtil.loadStructure
import com.shenlan.cnais.ais.server.HexUtil.xorEncrypt
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.UP_CONNECT_REQ
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.UP_DISCONNECT_REQ
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.UP_DISCONNECT_RSP
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.UP_LINKTEST_REQ
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.UP_LINKTEST_RSP
import com.shenlan.cnais.ais.server.oceanboat.OceanBoatConstant.UP_REAL_LOCATION
import com.shenlan.cnais.util.MinaModel
import com.shenlan.cnais.util.dateFormat
import com.shenlan.cnais.util.intToStr
import com.shenlan.cnais.util.log
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import khttp.post
import org.apache.mina.core.session.IoSession
import org.json.JSONObject
import java.util.*
import java.util.concurrent.TimeUnit

class OceanBoatManager: BaseManager {
    var autoStartDisposable: Disposable? = null
    var ip: String? = null
    var port: Int? = null
    var username: String? = null
    var password: String? = null
    var M1: Int = 1
    var IA1: Int = 1
    var IC1: Int = 1

    constructor()

    constructor(port: Int) {
        model = MinaModel(port)

        model?.messageReceived = { data, session ->
            log.info(data)
            deal(data, session)
        }

        model?.start()
    }

    constructor(ip: String, port: Int, username: String, password: String) {
        this.ip = ip
        this.port = port
        this.username = username
        this.password = password

        autoStartDisposable = Observable.interval(1, 1, TimeUnit.MINUTES).subscribe {
            val json = JSONObject().apply {
                put("username", username)
                put("password", password)
            }

            val response = post(
                url = "http://$ip:$port/getShipInfo",
                json = json,
                headers = mapOf(
                    "Content-Type" to "application/json"
                )
            )

            val result = response.text
        }
    }

    fun deal(data: String, session: IoSession?) : OceanBoatDataStructure {
        val structure = loadStructure(data, OceanBoatDataStructure::class.java)

        if (structure.messageHeader.encryptFlag == 1)
            xorEncrypt(structure, this)

        when (structure.messageHeader.msgType) {
            UP_CONNECT_REQ -> {
                structure.body = loadStructure(structure.messageBody, UpConnectReq::class.java)
                session?.write("00")
            }
            UP_DISCONNECT_REQ -> {
                structure.body = loadStructure(structure.messageBody, UpDisconnectReq::class.java)
                session?.write(UP_DISCONNECT_RSP)
            }
            UP_LINKTEST_REQ -> {
                session?.write(UP_LINKTEST_RSP)
            }
            UP_REAL_LOCATION -> {
                structure.body = loadStructure(structure.messageBody, UpRealLocation::class.java)
                session?.write((structure.body as UpRealLocation).msgId)

                val rawData = RawData().apply {
                    upRealLocation = structure.body as UpRealLocation
                    hash = "${upRealLocation.msgId} ${upRealLocation.utc} ${structure.messageBody}".hashCode()
                    this.data = structure.messageBody
                    fromType = "12"
                    date = intToStr(upRealLocation.utc.toInt())
                }
//                StationManageUtil.parent?.offer(rawData)
            }
            else -> {
                log.error("Unknown message type: ${structure.messageHeader.msgType}, data: $data")
            }
        }

        return structure
    }
}