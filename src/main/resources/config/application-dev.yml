spring:
    profiles:
        active: dev
    devtools:
        restart:
            enabled: true
        livereload:
            enabled: false
    datasource:
        type: org.springframework.jdbc.datasource.DriverManagerDataSource
        url:  ********************************************************************************************************************
        username: root
        password: SH<PERSON><PERSON><PERSON>@2016
        driver-class-name: com.mysql.jdbc.Driver

server:
    port: 11202

application:
      active: dev
      receiveMq: true
      chUrl: *******************************************
      hiFleetUrl: https://api.hifleet.com
      smsUrl: dysmsapi.aliyuncs.com