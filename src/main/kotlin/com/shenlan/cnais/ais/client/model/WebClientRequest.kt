package com.shenlan.cnais.ais.client.model

import com.shenlan.cnais.ais.RelatedProperty
import com.shenlan.cnais.util.intToStr

open class BaseHeilongjiangRequest {
    var authSigh: String = ""
    var authAppid: String = ""
    var timestamp: String = ""
    var dataSign: String = ""
    var dataSignAlgorithm: String = ""
    var type: String = ""

    constructor()
}

class HeilongjiangPositionRequest: BaseHeilongjiangRequest() {
    var sender: String = ""
    var receiver: String = ""
    var data: HeilongjiangPositionData = HeilongjiangPositionData()
}

class HeilongjiangPositionData {
    var random: String = ""
    var positions: MutableList<HeilongjiangPosition> = mutableListOf()
}

class HeilongjiangPosition {
    @RelatedProperty("lng") var lng: String = ""
        get() {
            return if (lngDir == "E")
                field
            else
                ((field.toDoubleOrNull() ?: 0.0) * -1.0).toString()
        }
    @RelatedProperty("lat") var lat: String = ""
        get() {
            return if (latDir == "N")
                field
            else
                ((field.toDoubleOrNull() ?: 0.0) * -1.0).toString()
        }
    var lngDir: String = ""
    var latDir: String = ""
    var valid: Boolean = true
    @RelatedProperty("cog") var directory: String = ""
    var height: String = ""
    @RelatedProperty("sog") var speed: String = ""
        get() = String.format("%.1f", (field.toDoubleOrNull() ?: 0.0) / 10.0)
    var dataTime: Long = 0L
    @RelatedProperty("time") var time: String = ""
        get() = intToStr((dataTime / 1000).toInt())
}

class HeilongjiangAlarmRequest: BaseHeilongjiangRequest() {
    var sender: String = ""
    var receiver: String = ""
    var data: HeilongjiangAlarmData = HeilongjiangAlarmData()
}

class HeilongjiangAlarmData {
    var alarmText: String = ""
    var alarmType: String = ""
    var alarmTime: Long = 0L
    var onOrOff: Boolean = true
}
