package com.shenlan.cnais.ais.server.beidou

import com.shenlan.cnais.ais.server.BaseManager
import com.shenlan.cnais.ais.server.beidou.BDShipConstant.DATA_PUSH_USER
import com.shenlan.cnais.auto.Datapushuser
import com.shenlan.cnais.util.MinaModel
import com.shenlan.cnais.util.SM4Util

class BDShipManager: BaseManager {

    constructor()

    constructor(port: Int) {
        this.model = MinaModel(port)

        model?.messageReceived = { data, session ->
            if (session.containsAttribute(DATA_PUSH_USER)) {
                val dataPushUser = session.getAttribute(DATA_PUSH_USER) as Datapushuser
                BDShipUtil.deal(data, dataPushUser)
            } else {
                val dataPushUser = BDShipUtil.checkLogin(data)
                if (dataPushUser.username.isNotEmpty()) {
                    session.setAttribute(DATA_PUSH_USER, dataPushUser)
                    session.write(SM4Util.encryptECB("{\"rlt\":0,\"info\":\"success\"}"))
                } else {
                    session.write(SM4Util.encryptECB("{\"rlt\":1,\"info\":\"error\",\"data\":\"用户名密码错误！\"}"))
                }
            }
        }

        model?.start()
    }
}