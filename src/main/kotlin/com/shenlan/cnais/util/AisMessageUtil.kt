package com.shenlan.cnais.util

import com.shenlan.cnais.ais.Aisc
import com.shenlan.cnais.ais.RawData
import java.text.SimpleDateFormat
import java.util.*

class AisMessageUtil {
    private var preRawData: RawData? = null
    var type = "0"

    fun dealData(data: String, date: Date = Date()) {
        val rawData = if (type == "11") {
            RawData().apply { this.data = data; this.message = data; this.hash = this.message.hashCode(); this.ifStandardHead = false }
        } else {
            RawData("" , SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date), data) //8000ms 处理8百万条数据 1ms处理1000条 // 逻辑辖区延时很高 是因为数据少10s一次vdo所以只有下一个vdo来了才能发出去 如果是vdo并且是4号报文就直接发出去 //        if (rawData.head == Aisc.VDO && rawData.content[0] == '4') { //            offer(rawData) //            return //        }
        }
        rawData.fromType = type

        if (rawData.fromType == "11") {
        } else {
            if (rawData.ifVdmVdo || rawData.head == Aisc.VSI) { //思路是 把一组先分好 然后看下一条是不是vsi 是的话带上vsi发出去 不是的话直接发出去
                if (preRawData == null) { //最开始preRawData为空的情况
                    preRawData = rawData
                } else {
                    if (rawData.ifFirst) {
                        preRawData = rawData
                    } else if (rawData.ifVdmVdo) {
                        preRawData!!.add(rawData)
                    } else {
                        preRawData?.vsi = rawData
                    }
                }
            } else {

            }
        }

    }

    fun dealData2(data: String, date: Date = Date(), func: (data: RawData) -> Unit) {
        val rawData = RawData("" , SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date), data) //8000ms 处理8百万条数据 1ms处理1000条 // 逻辑辖区延时很高 是因为数据少10s一次vdo所以只有下一个vdo来了才能发出去 如果是vdo并且是4号报文就直接发出去 //        if (rawData.head == Aisc.VDO && rawData.content[0] == '4') { //            offer(rawData) //            return //        }
        rawData.fromType = type
        if (rawData.ifVdmVdo || rawData.head == Aisc.VSI) { //思路是 把一组先分好 然后看下一条是不是vsi 是的话带上vsi发出去 不是的话直接发出去
            if (preRawData == null) { //最开始preRawData为空的情况
                preRawData = rawData
            } else {
                if (rawData.ifFirst) {

                    preRawData = rawData
                } else if (rawData.ifVdmVdo) {
                    preRawData!!.add(rawData)
                } else {
                    preRawData?.vsi = rawData
                }
            }
        } else {
        }
    }
}
