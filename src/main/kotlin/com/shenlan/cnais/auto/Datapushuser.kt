package com.shenlan.cnais.auto

import com.shenlan.BaseMapper
import com.shenlan.BaseModel
import com.shenlan.BaseSearch
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

class Datapushuser: BaseModel() {
    var username: String = ""
    var account: String = ""
    var password: String = ""
    var dataType: String = ""   // 0: 密文, 1: 明文
    var fromType: String = ""
}

class DatapushuserSearch: BaseSearch() {}

@Mapper
interface DatapushuserMapper: BaseMapper<Datapushuser> {
    @Select("""
        select * from tbl_datapushuser where username = #{param1}
    """)
    fun getInfoByUsername(username: String): Datapushuser
}