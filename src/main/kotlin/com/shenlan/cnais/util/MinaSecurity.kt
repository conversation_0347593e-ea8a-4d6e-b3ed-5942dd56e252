package com.shenlan.cnais.util

import com.shenlan.cnais.Application
import com.shenlan.cnais.auto.*
import dk.dma.ais.sentence.SentenceLine
import kong.unirest.Unirest
import org.apache.mina.core.buffer.IoBuffer
import org.apache.mina.core.filterchain.IoFilter
import org.apache.mina.core.filterchain.IoFilterAdapter
import org.apache.mina.core.session.IoSession
import java.net.InetSocketAddress
import java.util.*
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/**
 * Created by admin on 2022/3/3.
 */
class ApiBroadcast {
    /*

        //1.用户名
        var account: String? = null
        //2.密码
        var password: String? = null
        //3.播发报文
        var vdm: String? = null
        //4.播发基站mmsi
        var stationMmsi: String? = null
        //5.是否指定时隙
        var ifTsa: Int? = null                         //0:否 1:是
        //6.指配时隙语句
        var tsa: String? = null
    */
    var uid: String? = null                        //播发用户的ID
    var sentences: String? = null                  //多个语句之间用空格分隔
    var sentenceList: List<String>? = null
}

object MinaSecurity {
    var executor: ThreadPoolExecutor = ThreadPoolExecutor(3, 100, 60L, TimeUnit.SECONDS, LinkedBlockingQueue())

    //    var maxNumber = 2 // 最大并发连接数
    //    var sessionTime = 1000 * 60 * 2
    //最大连接数需要限制
    var concurrentcy = mutableMapOf<String, Int>()
    var sessionList = mutableListOf<IoSession>()
    var stationconfig: Stationconfig? = null //在用户登录的时候需要链路信息
    var systemUsername = "root"
    var systemPassword = "SHENLAN@2016"
    var DATA_USER = "datauser"
    var USER_FLOWID = "USER_FLOWID"

    //1分钟内失败3次需要封禁3分钟
    var failMap = mutableMapOf<String, MutableList<Long>>()

    //针对某些用户连续连接和断连的情况 10秒内重连大于5次封禁24小时
    var successMap = mutableMapOf<String, MutableList<Long>>()

    @Volatile var flag = false

    fun findIp(session: IoSession?): String? {
        return session?.let { (it.remoteAddress as InetSocketAddress).address.toString() }
    }

    fun getAccount(message: Any): List<String>? {
        var msgStr = ioBufferToString(message as IoBuffer)
        if (!msgStr.startsWith("\u0001")) {
            return null
        }
        msgStr = msgStr.replace("\r\n", "")
        var list = msgStr.substring(1, msgStr.length - 1).trim().split("\u0000").toMutableList()
        return list
    }

    fun ioBufferToString(ioBuffer: IoBuffer): String {
        val str = StringBuilder()
        while (ioBuffer.hasRemaining()) {
            str.append(ioBuffer.get().toChar())
        }
        ioBuffer.flip()
        return str.toString()
    }

    fun str16ToAsci(str: String): String {
        var res = ""
        for (i in 0..str.length - 2 step 2) {
            res += Integer.parseInt(str.substring(i, i + 2), 16).toChar()
        }
        return res
    }

//    fun getDatauser(username: String, password: String, session: IoSession): Datauser? { //root1 自测账号不加数据库记录
//        if ((username == systemUsername || username == "root1") && password == systemPassword) {
//            return Datauser().apply {
////                type = 0
//                account = username
//            }
//        } else { //根据数据库校验
//            var user = DatauserUtil.checkAccount(username, password)
//            return user
//        }
//    }

//    fun saveUserdataflow(session: IoSession, userId1: String, account1: String) {
//        var userDataFlowId = uuid()
//        session.setAttribute(USER_FLOWID, userDataFlowId) //一开始把userDataFlowId 放到Datauser对象里 会出现更新流量的时候只更新最后一条 是因为datauser对象是同一个
//        getBean(UserdataflowMapper::class.java).insert(Userdataflow().apply {
//            id = userDataFlowId
//            userId = userId1
//            account = account1
//            linkOrgId = stationconfig!!.id
//            status = "0"
//            sourceIp = (session.remoteAddress as InetSocketAddress).address.hostAddress
//            sourcePort = (session.remoteAddress as InetSocketAddress).port.toString()
//            destinationIp = (session.localAddress as InetSocketAddress).address.hostAddress
//            destinationPort = (session.localAddress as InetSocketAddress).port.toString()
//            connectDate = Date()
//        })
//    }

    /**
     *
     */
    fun authentication(message: Any, session: IoSession) {
        var ip = (session.remoteAddress as InetSocketAddress).address.hostAddress
        var port = (session.remoteAddress as InetSocketAddress).port.toString()
        if (session.containsAttribute(DATA_USER)) {
            var data = ioBufferToString(message as IoBuffer)
            log.info("receive ${ip} ${port} ${data}")
//            dealBroad(session.getAttribute(DATA_USER) as Datauser, data)
        } else {
            var usernameAndPass = getAccount(message)
            if (usernameAndPass == null) {
                log.info("${ip} ${port} login format error")
                TimeUnit.SECONDS.sleep(3)
                session.write(IoBuffer.wrap("format error ".toByteArray()))
                session.close(false)
            } else {
//                var datauser = getDatauser(usernameAndPass[0], usernameAndPass[1], session)
//                if (datauser == null) {
//                    log.info("${ip} ${port} login error: username:${usernameAndPass[0]}, password ${usernameAndPass[1]}") //cxdcs 这个用户把他禁用了 相当于他一连接就把他断了 然后他又一直连 导致日志很多 增加sleep
//                    TimeUnit.SECONDS.sleep(3)
//                    session.write(IoBuffer.wrap("username or password error ".toByteArray()))
//                    session.close(false)
////                    loginFail(usernameAndPass[0])
//                } else {
//                    /**
//                     * 2025-04-11 添加过滤规则
//                     */
//                    try {
//                        val dataFilterRules = getBean(DatafilterruleMapper::class.java).getByDataUserId(datauser.id)
//                        dataFilterRules.forEach { rule ->
//                            rule.compareValueSet = rule.compareValue.split(",").toSet()
//                        }
//                        // 数据过滤规则
//                        if (dataFilterRules.isNotEmpty())
//                            datauser.dataFilterRulesArr.addAll(dataFilterRules)
//
//                        /**
//                         * 2025-04-28
//                         * 海南局用户需要接收特定MMSI
//                         */
//                        if (datauser.account == "hainanju") {
//                            datauser.dataFilterRulesArr.add(Datafilterrule().apply {
//                                compareValueSet = getBean(ConfigMapper::class.java).getValueByCode("HAINANJU_MMSIS").split(",").toSet()
//                                conditi = "1"
//                                columnCode = "********"
//                            })
//                        }
//
//                        // 允许接收区域
//                        if (datauser.acceptableArea.isNotEmpty())
//                            datauser.acceptableAreaList.addAll(datauser.acceptableArea.split(","))
//                    } catch (e: Exception) {
//                        log.error("添加过滤规则失败，${e.message}")
//                    }
//
//
//                    log.info("$ip $port login success: username:${usernameAndPass[0]}, password ${usernameAndPass[1]}")
//                    session.setAttribute(DATA_USER, datauser)
//                    datauser.init(session)
//                    if (!(datauser.account == systemUsername || datauser.account == "root1")) {
//                        saveUserdataflow(session, datauser.id, datauser.account)
//                    }
//                }
            }
        }
    }

    fun saveUserdataflow(session: IoSession, userId1: String, account1: String) {
        var userDataFlowId = uuid()
        session.setAttribute(USER_FLOWID, userDataFlowId) //一开始把userDataFlowId 放到Datauser对象里 会出现更新流量的时候只更新最后一条 是因为datauser对象是同一个
        getBean(UserdataflowMapper::class.java).insert(Userdataflow().apply {
            id = userDataFlowId
            userId = userId1
            account = account1
//            linkOrgId = stationconfig!!.id
            status = "0"
            sourceIp = (session.remoteAddress as InetSocketAddress).address.hostAddress
            sourcePort = (session.remoteAddress as InetSocketAddress).port.toString()
            destinationIp = (session.localAddress as InetSocketAddress).address.hostAddress
            destinationPort = (session.localAddress as InetSocketAddress).port.toString()
            connectDate = Date()
        })
    }

    var localIpStr = ""
    fun getLocalipStr(): String {
        try {
            if (localIpStr == "") {
                var ipList = getIp()
                var port = Application.context!!.environment.getProperty("server.port")
                var nodeList = getBean(NodeinfoMapper::class.java).getAll()
                var nodeInfo = nodeList.filter { ipList.contains(it.dataIp) && it.dataPort == port }.firstOrNull()
                localIpStr = "${nodeInfo?.dataIp}:${nodeInfo?.servicePort}"
            }
        } catch (e: Exception) {

        }
        return localIpStr
    }

    fun getUserIpStr(session: IoSession): String {
        var ipList = getIp()
        var ip = (session.remoteAddress as InetSocketAddress).address.hostAddress
        var port = (session.remoteAddress as InetSocketAddress).port.toString()
        var nodeList = getBean(NodeinfoMapper::class.java).getAll()
        var nodeInfo = nodeList.filter { ipList.contains(it.dataIp) && it.dataPort == port }.firstOrNull()

        return "${ip}:${port} ⇒ ${getLocalipStr()}"
    }

    var userMap = mutableMapOf<String, String>()
//    fun dealBroad(datauser: Datauser, data: String) {
//        try {
//            var sentence = SentenceLine(data)
//            when (sentence.formatter) {
//                "ABM", "BBM" -> {
//                    if (sentence.fields[1] == sentence.fields[2]) {
//                        var predata = userMap.get(datauser.id) ?: ""
//                        send(datauser.id, (predata + " " + data).trim())
//                        userMap.remove(datauser.id)
//                    } else {
//                        if (userMap.containsKey(datauser.id)) {
//                            userMap.put(datauser.id, userMap[datauser.id]!! + " " + data)
//                        } else {
//                            userMap.put(datauser.id, data)
//                        }
//                    }
//                }
//                "AIR" -> {
//                    send(datauser.id, data)
//                }
//            }
//        } catch (e: Exception) {
//            errorlog(e)
//        }
//    }

    fun send(id: String, data: String) {
        var url = sqlMapper.queryStr("select concat('http://',webip,':',webPort) from tbl_nodeinfo where id='1'") + "/api/open/apiBroadcast" //        var url = "http://*************:11103" + "/api/open/apiBroad"
        log.info("send: ${data}")
        Unirest.post(url).header("Content-Type", "application/json").body(ApiBroadcast().apply {
            uid = id
            sentences = data
        }.toJsonString).asString()
    }

    //并发控制
//    fun dealConcurrent(datauser: Datauser, session: IoSession): Boolean {
//        if (concurrentcy.containsKey(datauser.account)) {
//            var num = concurrentcy[datauser.account]!!
//            if (datauser.dataAbnormal?.concurrentNumber != null) {
//                if (num >= datauser.dataAbnormal?.concurrentNumber!!) {
//                    log.info("${datauser.account} concurrent is limit")
//                    return false
//                } else {
//                    concurrentcy[datauser.account] = 1 + concurrentcy[datauser.account]!!
//                }
//            }
//        } else {
//            concurrentcy[datauser.account] = 1
//        }
//        return true
//    }
//
//    //1分钟内失败3次需要封禁3分钟
//    fun loginFail(account: String) {
//        if (failMap.containsKey(account)) {
//            var list = failMap[account]!!
//            list.add(System.currentTimeMillis() / 1000)
//            if (sqlMapper.queryInt("select count(1) from tbl_userinfo where account='${account}' and sysdeleted=0 and status=0") == 1) {
//                list.clear()
//            } else {
//                if (list.size >= 3 && (list[2] - list[0]) <= 60) {
//                    sqlMapper.update("update tbl_userinfo set status=0, bannedStatus='1',bannedMinute=3,BannedTime=date_add(now(),interval 3 minute) where account='${account}'")
//                    insertEvent(getUserName(account), account, "Login.Account2")
//                    insertAlarm(account)
//                    list.clear()
//                }
//            }
//        } else {
//            failMap.put(account, mutableListOf(System.currentTimeMillis() / 1000))
//        }
//    }
//
//    //60秒内成功5次需要封禁24小时
//    fun loginSuccessCount(account: String): Boolean {
//        log.info("loginSuccessCount: ${account} ${account.startsWith("root")}")
//        if (account.startsWith("root")) { //为什么加了排除root1 但是还是有root1的alarm日志
//            return true
//        }
//        if (successMap.containsKey(account)) {
//            var list = successMap[account]!!
//            list.add(System.currentTimeMillis() / 1000)
//            if (list.size > 5) {
//                list.clear()
//            }
//            log.info("loginSuccessCount: ${account} ${list.size} ${list.toJsonString}")
//            if (list.size >= 5 && (list[4] - list[0]) <= 60) {
//                sqlMapper.update("update tbl_userinfo set status=0, bannedStatus='1',bannedMinute=24*60,BannedTime=date_add(now(),interval 24*60 minute) where account='${account}'")
//                insertEvent(getUserName(account), account, "Login.Account8")
//                insertAlarm2(account)
//                list.clear()
//                return false
//            }
//        } else {
//            successMap.put(account, mutableListOf(System.currentTimeMillis() / 1000))
//        }
//        return true
//    }

    fun loginSuccess(account: String) {
        failMap.remove(account)
    }

    fun getUserId(account: String) = sqlMapper.queryStr("select id from tbl_userinfo where account='${account}' and sysdeleted=0 limit 1") ?: ""

    fun getUserName(account: String) = sqlMapper.queryStr("select username from tbl_userinfo where account='${account}' and sysdeleted=0 limit 1") ?: ""

    //由于alarm表总出问题 用其他线程执行 防止数据用户数据量出问题
//    fun insertAlarm(account: String) {
//        executor.execute {
//            try {
//                var event = EventUtil.eventMap["User.actionErr1"]!!
//                Alarm().apply {
//                    id = uuid()
//                    linkOrgId = getCurrentNodeLinkOrgId()
//                    orgName = getCurrentNodeLinkOrgName()
//                    eventId = event.id
//                    alarmTypeCode = event.eventModule
//                    alarmContent = event.eventContent.format(getUserName(account), account, orgName)
//                    alarmDate = Date()
//                    recoverDate = Date()
//                    eventType = event.ifAlarm
//                    alarmStatus = 1
//                    stationId = getUserId(account)
//                    _insert(this)
//                }
//            } catch (e: Exception) {
//                errorlog(e)
//            }
//        }
//    }
//
//    fun insertAlarm2(account: String) {
//        executor.execute {
//            try {
//                var event = EventUtil.eventMap["User.actionErr3"]!!
//                Alarm().apply {
//                    id = uuid()
//                    linkOrgId = getCurrentNodeLinkOrgId()
//                    orgName = getCurrentNodeLinkOrgName()
//                    eventId = event.id
//                    alarmTypeCode = event.eventModule
//                    alarmContent = event.eventContent.format(getUserName(account), account)
//                    alarmDate = Date()
//                    recoverDate = Date()
//                    eventType = event.ifAlarm
//                    alarmStatus = 1
//                    stationId = getUserId(account)
//                    _insert(this)
//                }
//            } catch (e: Exception) {
//                errorlog(e)
//            }
//        }
//    }
//
//    fun insertEvent(username: String, account: String, eventId1: String, userid: String = "", ipStr: String = "") {
//        executor.execute {
//            try {
//                if (!account.startsWith("root")) {
//                    var event = EventUtil.eventMap[eventId1]!!
//                    Alarm().apply {
//                        id = uuid()
//                        linkOrgId = getCurrentNodeLinkOrgId()
//                        orgName = getCurrentNodeLinkOrgName()
//                        eventId = event.id
//                        alarmTypeCode = event.eventModule
//                        if (ipStr.notEmpty()) {
//                            alarmContent = event.eventContent.format(username, account, ipStr)
//                        } else {
//                            alarmContent = event.eventContent.format(username, account)
//                        }
//                        alarmDate = Date()
//                        eventType = event.ifAlarm
//                        stationId = userid
//                        _insert(this)
//                    }
//                }
//            } catch (e: Exception) {
//                errorlog(e)
//            }
//        }
//
//    }

    //    fun concurrCount(session: IoSession?, account: String) {
    //        var number = AtomicInteger(if (concurrentcy[ip] == null) 0 else concurrentcy[ip]!!)
    //        if (concurrentcy.containsKey(account)) {
    //            if (concurrentcy[ip]!! >= maxNumber) {
    //                session?.write(IoBuffer.wrap("您的IP连接数超过了最大连接数！".toByteArray()))
    //                session!!.close(false)
    //                return
    //            }
    //        }
    //        var incrementAndGet = number.incrementAndGet()
    //        concurrentcy[ip!!] = incrementAndGet
    //        session?.let { sessionList.add(it) }
    //        sessionActive()
    //    }

//    fun decrement(session: IoSession) {
//        if (session.containsAttribute(MinaSecurity.DATA_USER)) {
//            var datauser = session.getAttribute(MinaSecurity.DATA_USER) as Datauser
//            if (concurrentcy.containsKey(datauser.account)) {
//                concurrentcy[datauser.account] = concurrentcy[datauser.account]!! - 1
//            }
//        }
//    }

    fun sessionActive() {
        synchronized(this, {
            if (!flag) {
                flag = true //                SessionThread().start()
            }
        })
    }
}

//class SessionThread : Thread() {
//
//    override fun run() {
//        while (true) {
//            if (MinaSecurity.sessionList.size == 0) {
//                MinaSecurity.flag = false
//                break
//            }
//            MinaSecurity.sessionList.forEach {
//                if ((System.currentTimeMillis() - it.creationTime) >= MinaSecurity.sessionTime) {
//                    it.write(IoBuffer.wrap("您的IP有效期失效！".toByteArray()))
//                    it.close(false)
//                    log.info("客户端：{}->有效期到期，退出连接... ", MinaSecurity.findIp(it))
//                }
//            }
//            TimeUnit.MILLISECONDS.sleep((MinaSecurity.sessionTime / 2).toLong())
//        }
//    }
//}

class MyFilter : IoFilterAdapter() {

    //    override fun onPreAdd(parent: IoFilterChain?, name: String?, nextFilter: IoFilter.NextFilter?) {
    ////        MinaSecurity.concurrCount(parent?.session)
    //    }
    //
    //    override fun onPreRemove(parent: IoFilterChain?, name: String?, nextFilter: IoFilter.NextFilter?) {
    //        MinaSecurity.decrement(parent?.session)
    //    }

    override fun sessionClosed(nextFilter: IoFilter.NextFilter?, session: IoSession?) {
//        MinaSecurity.decrement(session!!)
        super.sessionClosed(nextFilter, session)
    }

    override fun messageReceived(nextFilter: IoFilter.NextFilter?, session: IoSession?, message: Any?) {
        try {
            MinaSecurity.authentication(message!!, session!!)
        } catch (e: Exception) {
            errorlog(e)
        }
        super.messageReceived(nextFilter, session, message)
    }

}

