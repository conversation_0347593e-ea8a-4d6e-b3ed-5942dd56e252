package com.shenlan.cnais.util

import com.shenlan.Result
import com.shenlan.cnais.auto.Stationconfig
import com.shenlan.cnais.auto.StationconfigMapper
import com.shenlan.cnais.web.WebSocket
import com.shenlan.cnais.web.WebSocketManager
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import org.apache.mina.core.buffer.IoBuffer
import org.apache.mina.core.service.IoHandler
import org.apache.mina.core.service.IoService
import org.apache.mina.core.service.IoServiceListener
import org.apache.mina.core.session.IdleStatus
import org.apache.mina.core.session.IoSession
import org.apache.mina.filter.codec.ProtocolCodecFilter
import org.apache.mina.filter.codec.textline.LineDelimiter
import org.apache.mina.filter.codec.textline.TextLineCodecFactory
import org.apache.mina.transport.socket.nio.NioDatagramAcceptor
import org.apache.mina.transport.socket.nio.NioDatagramConnector
import org.apache.mina.transport.socket.nio.NioSocketAcceptor
import org.apache.mina.transport.socket.nio.NioSocketConnector
import java.net.InetSocketAddress
import java.nio.charset.Charset
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread


/**
 * Created by Administrator on 2021/10/18.
 */

object MinaUtil {

    fun createBaseServer(port: Int): MinaModel {//创建最简单的server
        var model = MinaModel(port)
        model.start()
        return model
    }

    fun createBaseClient(ip: String, port: Int): MinaModel {//创建最简单的client
        var model = MinaModel(ip, port)
        model.start()
        return model
    }

    fun createAisPlatClient(stationconfig: Stationconfig): MinaModel {//基站管理端口8021 和平台的连接是一样的 要用户名和密码
        var model = MinaModel(stationconfig.stationIp!!, stationconfig.startPort!!.toInt())
        model.sessionOpened = { it ->
            //这里的发送是直接以16进制的情况发送 在末尾没有加\r\n(0d0a) 但是如果是字符串的话 mina就会自动加上\r\n
            it.write(IoBuffer.wrap(getHexArray(stationconfig.userName!!, stationconfig.password!!)))
            it.write("\$PSTT,301,313*00")
        }
        model.messageReceived = { it, session ->
            //            println(it)
            //            log.info(it)
            if (it.contains("PSTT")) {
                log.info(it)
            }
            //            if (WebSocketManager.webSocketSet.size > 0) {
            //                sendWebsocket(it)
            //            }
        }
        model.start()
        return model
    }

    fun createWebtest(stationconfig: Stationconfig): MinaModel {
        var model = MinaModel(stationconfig.stationIp!!, stationconfig.startPort!!.toInt(), false)
        model.sessionOpened = { it ->
            if (stationconfig.userName.notEmpty()) {
                it.write(IoBuffer.wrap(getHexArray(stationconfig.userName!!, stationconfig.password!!)))
            }
        }
        model.messageReceived = { it, session ->
            //            log.info(it)
            if (WebSocketManager.webSocketSet.size > 0) {
                sendWebsocket(it)
            }
        }
        model.start()
        return model
    }

    fun getHexArray(username: String = "tjhbc", password: String = "tjhbc"): ByteArray {
        log.info("getHexArray $username $password")
        val stringArray = getVerification(username, password).split(" ")
        val arr = ByteArray(stringArray.size)
        var i = 0
        for (b in stringArray) {
            if (b == "FF") {
                arr[i++] = -1
            } else {
                arr[i++] = java.lang.Byte.parseByte(b, 16)
            }
        }
        return arr
    }

    fun getVerification(username: String = "tjhbc", password: String = "tjhbc"): String {
        var rltList = mutableListOf<String>().apply { add("01") }
        username.forEach { rltList.add(asciTo16Str(it.toString()).toUpperCase()) }
        rltList.add("00")
        password.forEach { rltList.add(asciTo16Str(it.toString()).toUpperCase()) }
        rltList.add("00")
        return rltList.joinToString(" ")
    }

    fun createStationMockServer(port: Int, list: MutableList<String>): MinaModel {//模拟基站server隔100ms 发送报文数据
        var model = MinaModel(port)
        model.start()
        model.sessionOpened = { it ->
            log.info("sessionOpened")
            thread {
                TimeUnit.SECONDS.sleep(3)
                while (true) {
                    list.forEach { a ->
                        it.write(a)
                        TimeUnit.SECONDS.sleep(1)
                    }
                }
            }
        }
        return model
    }

    fun createStationMockServer(port: Int): MinaModel {//模拟基站server隔100ms 发送报文数据
        var model = MinaModel(port)
        model.start()
        model.sessionOpened = { it ->
            log.info("sessionOpened")
            thread {
                while (true) {
                    //                    var data = MockUtil.getMockAisData()
                    //                    it.write(data)
                    //                    it.write("\$ABVSI,Huangbaizui,8,093124,932,-99,4*02")
                    //                    TimeUnit.MILLISECONDS.sleep(100)
                    //                    it.write(data)
                    //                    it.write("\$ABVSI,Huangbaizui,8,093124,932,-99,4*02")
                    //                    TimeUnit.MILLISECONDS.sleep(100)
                }
            }
        }
        return model
    }


    // *海事内网连接ais数据  198.12.175.8   01 74 6A 68 62 63 00 74 6A 68 62 63 00 (tjhbc)
    // * ais内网连接ais数据 192.168.73.131
    // */
    fun asciTo16Str(str: String): String {
        var tmp = ""
        for (i in 0..str.length - 1) {
            var t = Integer.toHexString(str[i].toInt())
            if (t.length == 1) {
                t = "0" + t
            }
            tmp += t
        }
        return tmp
    }

    var testMinaModel: MinaModel? = null
    fun openDataStream(station: Stationconfig) {
        testMinaModel = createWebtest(station)
    }

    fun closeDataStream(info: Stationconfig) {
        testMinaModel?.closeClient()
        //        testMultStationManage?.close()
    }


    //数据日志的推送 和位置报告的推送
    fun sendWebsocket(message: String) {
        WebSocket.sendInfo(message)
        //        WebSocket.sendInfo(WebsocketInfo().apply {
        //            type = "0"
        //            date = dateFormat(Date())
        //            data = message
        //        })
    }

    //    fun ifNormal(stationconfig: Stationconfig): Boolean {
    //        var model = MinaModel(stationconfig.stationIp!!, stationconfig.startPort!!.toInt(), false)
    //        model.sessionOpened = { it ->
    //            if (stationconfig.userName.notEmpty()) {
    //                it.write(IoBuffer.wrap(MinaUtil.getHexArray(stationconfig.userName!!, stationconfig.password!!)))
    //            }
    //        }
    //        model.start()
    //        TimeUnit.SECONDS.sleep(1)  //1秒钟内没连上证明服务端有问题
    //        model.close()
    //        return model.ifNormal
    //    }

    //    fun ifAllNormal() {
    //        log.info("start ifAllNormal")
    //        getBean(StationconfigMapper::class.java).getAll().forEach { stationconfig ->
    //            sqlMapper.update("update tbl_stationconfig set ifnormal='${ifNormal(stationconfig).toChar()}' where id='${stationconfig.id}'")
    //        }
    //        log.info("end ifAllNormal")
    //    }
}

//记录连接端口的时间 和断线时间用来恢复数据
class TimeRecord {
    var firstStartTime = ""
    var exceptionTimeList = mutableListOf<Pair<String, String>>()
    var exceptionTime = ""
    var recoverTime = ""//恢复时间
    var ifException = false
    fun addRecoverTime() {
        if (firstStartTime == "") {
            firstStartTime = dateFormat(Date())
        } else {
            recoverTime = dateFormat(Date())
            ifException = false
            //            exceptionTimeList.add(Pair<String, String>(exceptionTime, recoverTime))
            //            log.info(exceptionTimeList.toJsonString)
        }
    }

    fun addExceptionTime() {
        ifException = true
        exceptionTime = dateFormat(Date())
    }
}

open class MinaModel {
    var ip: String = ""
    var port: Int = 0
    var ifServer = true
        get() = ip.isEmpty()

    var lastDataTime: Long = System.currentTimeMillis()
    var connector: NioSocketConnector? = null
    var clientSession: IoSession? = null  //客户端连接session
    //    var sessionList = CopyOnWriteArrayList<IoSession>()//需要线程安全 因为另一个线程遍历这个写数据出去 而有新的连接会add数据
    var acceptor: NioSocketAcceptor? = null
    var handler: BaseHandler? = null
    var noDataTime = 3   //无数据时间分钟
    var auToRepeatTime = 1 //自动循环时间分钟
    var ifAutoStart = false
    //    var retryTime = 0//第一次连接服务端 未成功默认连接3次
    var ifNormal = false //是否连接成功
    var exceptionMessage = "" //是否连接成功
    //    var timeRecord = TimeRecord()

    var lineCount = 0L//连接后接收到数据的总行数
    var byteCount = 0L//连接后接收到的字节数
    var autoStartDisposable: Disposable? = null //如果第一次连接基站失败 会重连 如果更新机基站 这个定时任务没关
    var stationconfig: Stationconfig? = null//在用户登录的时候需要链路信息
    var ifAuth = false
    var exceptionTime: Date? = null
    var restartInfo = mutableListOf<String>()//记录重启时间和信息 ,隔开
    var ifNoData = false//是否无数据
    var ifHEDESatellite = false // 和德卫星数据
    var fromType = ""
    //    var reconnectIoListener = ReconnectIoListener()
    open var sessionOpened = { it: IoSession -> }

    open var messageReceived = { it: String, session: IoSession ->
        log.info(it)
    }
    open var exceptionCaught = { it: IoSession?, message: String ->
        log.info("exceptionCaught ")
        //        timeRecord.addExceptionTime()
    }
    open var startClientException = {

    }
    open var exceptionRecover = {
        log.info("exceptionRecover")
        //        timeRecord.addRecoverTime()
    }
    open var sessionClosed = { it: IoSession? ->
        log.info("sessionClosed")
        //        timeRecord.addRecoverTime()
    }

    //    open var sessionNodata = { it: IoSession? ->
    //        log.info("sessionNodata")
    ////        timeRecord.addRecoverTime()
    //    }
    fun basicRecord(data: String) {//基础记录
        lineCount++
        byteCount += (data.length)
        lastDataTime = System.currentTimeMillis()
    }

    constructor()
    constructor(ip: String, port: Int, ifAutoStart: Boolean = false) {
        this.ip = ip
        this.port = port
        handler = BaseHandler(this)
        //        reconnectIoListener.minaModel = this
        this.ifAutoStart = ifAutoStart
        autoRestart()
    }

    constructor(ip: String, port: Int, ifAutoStart: Boolean = false, ifHEDESatellite: Boolean = false) {
        this.ip = ip
        this.port = port
        handler = BaseHandler(this)
        //        reconnectIoListener.minaModel = this
        this.ifAutoStart = ifAutoStart
        this.ifHEDESatellite = ifHEDESatellite
        autoRestart()
    }

    constructor(port: Int) {
        this.port = port
        handler = BaseHandler(this)
    }

    fun getSessionList(): MutableCollection<IoSession>? {
        return acceptor?.managedSessions?.values
    }

    open fun start(charset: String = "UTF-8") {
        if (ifServer) {
            startServer(charset)
        } else {
            startClient(charset)
        }
    }


    fun autoRestart() {//以前是用线程的方式现在改成rxjava  同一时间 这个调用多次,要把这个放到初始化那里 每一次重启又会建一个定时任务
        if (ifAutoStart) {
            autoStartDisposable = Observable.interval(1, auToRepeatTime.toLong(), TimeUnit.MINUTES).subscribe {
                ifNoData = (System.currentTimeMillis() - lastDataTime > noDataTime * 60 * 1000)// 出现过连上了青岛辖区但是数据没更新
                if (ifNoData) {
                    log.info("ifNoData:${ifNoData} ${ip} ${(System.currentTimeMillis() - lastDataTime) / 1000}s")
                    //                    sessionNodata(clientSession)
                }
                if (!ifNormal || ifNoData) {//5分钟没有数据
                    //                    log.error("ip: ${ip} port: ${port} no data ")
                    log.info("begin autoRestart ifNormal:${ifNormal} ifNoData:${ifNoData} ")
                    restart(if (ifNoData) "NoData" else exceptionMessage)
                    log.info("end autoRestart")
                }
            }
        }
    }


    open fun restart(message: String) {
        restartInfo.add(dateFormat(Date()) + "," + message)
        if (restartInfo.size > 100) {
            restartInfo.clear()
        }
        if (ifServer) {
            restartServer()
        } else {
            restartClient()
        }
    }

    fun restartClient() {
        closeClient()
        start()
    }

    fun close() {
        if (ifServer) {
            closeServer()
        } else {
            closeClient()
        }
        autoStartDisposable?.dispose()
    }

    fun closeClient() {
        try {
            //            clientSession?.close(true)
            //            log.info("closeClient1")
            connector?.dispose() //这里会调用sessionClose
            //            log.info("closeClient2")
            TimeUnit.SECONDS.sleep(1)//关闭立马在起一个端口会报错已绑定
            //            log.info("closeClient3")
        } catch (e: Exception) {
            log.error("closeClient ${e.message}")
        }
    }

    fun restartServer() {
        closeServer()
        start()
    }

    fun closeServer() {
        try {
            acceptor?.unbind()
            acceptor?.dispose()
            TimeUnit.SECONDS.sleep(1)//关闭立马在起一个端口会报错已绑定
        } catch (e: Exception) {
            log.error("closeServer ${e.message}")
        }
    }

    fun startServer(charset: String) {
        try {
            log.info("startServer port:${port}")
            acceptor = NioSocketAcceptor()
            acceptor!!.isReuseAddress = true
            val lineCodec = TextLineCodecFactory(Charset.forName(charset), LineDelimiter.CRLF, LineDelimiter.CRLF) //LineDelimiter.CRLF
            lineCodec.decoderMaxLineLength = 1024 * 10240 //10M
            lineCodec.encoderMaxLineLength = 1024 * 10240
            if (ifAuth) {
                acceptor!!.filterChain.addLast("myFilter", MyFilter())
//                MinaSecurity.stationconfig = stationconfig
                acceptor!!.filterChain.addLast("myChain", ProtocolCodecFilter(lineCodec))
            } else {
                acceptor!!.filterChain.addLast("myChain", ProtocolCodecFilter(lineCodec))
            }

            acceptor!!.handler = handler
            acceptor!!.bind(InetSocketAddress(port))
            exceptionRecover()
            ifNormal = true
        } catch (e: Exception) {
            ifNormal = false
            exceptionMessage = e.message ?: ""
            log.error(e.message) //            log.info("begin reconnect")
            //            TimeUnit.SECONDS.sleep(5)
            //            startServer()
        }
    }

    fun startClient(charset: String = "UTF-8") {
        try {
            log.info("begin connect: ${ip} ${port}")
            connector = NioSocketConnector() // 设置过滤器,选用Mina自带的过滤器一行一行读取代码
            when (fromType) {
                "17" -> connector!!.filterChain.addLast("myChain", ProtocolCodecFilter(TextLineCodecFactory(Charset.forName(charset), LineDelimiter.UNIX, LineDelimiter.UNIX)))
                else -> connector!!.filterChain.addLast("myChain", ProtocolCodecFilter(TextLineCodecFactory(Charset.forName(charset), LineDelimiter.CRLF, LineDelimiter.CRLF)))
            }
            connector!!.connectTimeoutMillis = 3 * 1000// 设置连接超时时间
            connector!!.handler = handler
            val future = connector!!.connect(InetSocketAddress(ip, port))//"*************", 4001))//(host, port))
            future.awaitUninterruptibly()
            clientSession = future.session
            exceptionRecover()
            ifNormal = true
            log.info("end connect: ${ip} ${port}")
            //            doConnectSuccess(clientSession!!)
            //            clientSession!!.write(IoBuffer.wrap(byteArray)) //发送验证信息
        } catch (e: Exception) {
            ifNormal = false
            exceptionMessage = e.message ?: ""
            log.error(e.message)
            connector?.dispose()
            startClientException()
            //            exceptionCaught(null, "startClientException")
            //            log.info("begin reconnect")
            //            TimeUnit.SECONDS.sleep(5)
            //            startClient()
        }
    }

    fun addSession(p0: IoSession) {
        log.info("addSession ${p0.remoteAddress}")
        //        sessionList.add(p0)
    }

    fun removeSession(p0: IoSession) {
        //        sessionList.remove(p0)
    }

    fun writeToClient(data: String) {
        getSessionList()?.forEach {
            //            if(it.isConnected)
            it.write(data)
        }
    }

    //以前使用 restartClient 重启 sessionclose和sessionexception 不好处理 改成使用listener处理断线重连 还是先不用这种方式
    //    fun reconnectClient(session: IoSession) {
    //        var flag = true
    //        var index = 0
    //        while (flag) {//第一次重连不计报警
    //            TimeUnit.SECONDS.sleep(3000)
    //            index++
    //            index = index % 1000
    //            flag = !reconnectClient(session, index)
    //            if (index > 1) {
    //                exceptionCaught(session,"reconn")
    //            }
    //        }
    //    }
    //
    //    fun reconnectClient(session: IoSession, index: Int): Boolean {
    //        try {
    //            log.info("begin reconnect ${index} ${session!!.remoteAddress.toString()}")
    //            var future = connector?.connect()!!
    //            future.awaitUninterruptibly()
    //            var tmpSession = future.session
    //            clientSession = future.session
    //            if (tmpSession.isConnected) {
    //                ifNormal = true
    //                exceptionRecover()
    //                log.info("reconnect success ${index} ${tmpSession!!.remoteAddress.toString()}")
    //            } else {
    //                ifNormal = false
    //                log.info("reconnect fail ${index} ${tmpSession!!.remoteAddress.toString()}")
    //            }
    //        } catch (e: Exception) {
    //            ifNormal = false
    //        }
    //        return ifNormal
    //    }
}

class BaseHandler(var minaModel: MinaModel) : IoHandler {

    override fun messageReceived(p0: IoSession?, p1: Any?) {
        minaModel.messageReceived(p1.toString(), p0!!)
        minaModel.lastDataTime = System.currentTimeMillis()
    }

    override fun sessionOpened(p0: IoSession?) {
        minaModel.sessionOpened(p0!!)
        //        minaModel.addSession(p0)
        //        clientSession!!.write(IoBuffer.wrap(byteArray))
    }

    override fun sessionClosed(p0: IoSession?) {//这里也会调用exceptionCaught
        //        minaModel.removeSession(p0!!)
        minaModel.sessionClosed(p0)
        log.info("sessionClosed ${p0!!.remoteAddress.toString()}..................")
        //        minaModel.exceptionCaught(p0, Exception())
    }

    override fun messageSent(p0: IoSession?, p1: Any?) {
    }

    override fun sessionCreated(p0: IoSession?) {
        log.info("已创建连接 ${p0!!.remoteAddress.toString()}..................")
    }

    override fun sessionIdle(p0: IoSession?, p1: IdleStatus?) {
    }

    override fun exceptionCaught(p0: IoSession?, p1: Throwable?) {//为什么p1?.message 为空
        log.error("BaseHandler exceptionCaught: ${p0!!.remoteAddress.toString()} ${p1 == null}" + p1?.message)
        if (p1 != null) {
            //            errorlog(p1)
        }
        minaModel.exceptionCaught(p0!!, p1?.message ?: "")
    }
}

//object LocalMinaModelReceiver {
//    private const val host = "127.0.0.1"
//    private const val port = 4010
//    private const val username = "jshsj2023"
//    private const val password = "jsHsj@2023"
//    private val messages = mutableListOf<String>()
////    var executor: ThreadPoolExecutor = ThreadPoolExecutor(1, 100, 60L, TimeUnit.SECONDS, LinkedBlockingQueue())
//    private val aispositionTempList = mutableListOf<AispositionTemp>()
//
//    //    @Scheduled(initialDelay = 1000 * 30, fixedDelay = (5 * 60 * 1000).toLong())
//    fun createLocalClient(receiveTime: Int): Result {
//        for (i in 0 until receiveTime) {
//            log.info("start receive local messages, host: $host, port: $port, receiveTime: ${receiveTime}s")
//            val model = MinaModel(host, port)
//            var updateCounter = 0
//            model.sessionOpened = { it ->
//                it.write(IoBuffer.wrap(MinaUtil.getHexArray(username, password)))
//            }
//            model.messageReceived = { it, _: IoSession ->
////            log.info(it)
//                messages.add(it)
//            }
//
//            model.start()
//            Thread.sleep(30 * 1000L)
//            model.close()
//
//            log.info("LocalMinaModelReceiver receive messages size: ${messages.size}")
//
//            Thread.sleep(3 * 1000)
//            dealMessage()
//            try {
//                aispositionTempList.distinctBy { it.id }.chunked(10000).forEach { chunk ->
//                    sqlMapper.update("delete from datacenter.tbl_aispositiontemp where id in (${chunk.map { it.id }.getInStr()})")
//                    getBean(AispositionTempMapper::class.java).insertList(chunk)
////                    _insertList(chunk)
//                    log.info("tbl_aispositiontemp update size: ${chunk.size}")
//                    updateCounter += chunk.size
//                }
//            } catch (e: Exception) {
//                errorlog(e)
//            }
//
//            aispositionTempList.clear()
//            log.info("LocalMinaModelReceiver aispositionTempList clear")
//            Thread.sleep(3 * 1000)
//        }
//
//        return Result.success
//    }

//    private fun updateList(list: MutableList<String>) {
//        val aispositionTempList = dealMessage(list)
//        var updateCounter = 0
//        if (aispositionTempList.isNotEmpty()) {
//            aispositionTempList.distinctBy { it.id }.chunked(10000).forEach { chunk ->
//                sqlMapper.update("delete from datacenter.tbl_aispositiontemp where id in (${chunk.map { it.id }.getInStr()})")
//                getBean(AispositionTempMapper::class.java).insertList(chunk)
////                    _insertList(chunk)
//                log.info("tbl_aispositiontemp update size: ${chunk.size}")
//                updateCounter += chunk.size
//            }
//        }
//        log.info("LocalMinaModelReceiver update size: $updateCounter")
//    }

//    private fun dealMessage(): MutableList<AispositionTemp> {
////        val aispositionTempList = mutableListOf<AispositionTemp>()
//        try {
//            messages.forEach { message ->
//                if (message.isNotEmpty()) {
//                    aispositionTempList.add(getAisPositionTemp(message))
//                }
//            }
//            messages.clear()
//            log.info("LocalMinaModelReceiver messages clear, aispositionTempList size: ${aispositionTempList.size}")
//        } catch (e: Exception) {
//            errorlog(e)
//        }
//        return aispositionTempList
//    }
//
//    private fun getAisPositionTemp(message: String) : AispositionTemp {
//        val plainText = message.replace("\"", "").replace("{", "").replace("}", "").split(",")
//        var plainTextMap = mutableMapOf<String, String>()
//        try {
//            plainTextMap = plainText.associate {
//                val kv = it.split(":")
//                if (kv.size < 2) {
//                    "ERROR" to "ERROR"
//                } else {
//                    kv[0] to kv[1]
//                }
//            }.toMutableMap()
//        } catch (e: Exception) {
//            log.error(plainText.joinToString(",") { it })
//        }
//
////        println(plainTextMap)
//        return AispositionTemp().apply {
//            plainTextMap.forEach { entry ->
//                when(entry.key) {
//                    "MMSI" -> {
//                        id = entry.value
//                        mmsi = entry.value
//                    }
//                    "LNG" -> longitude = entry.value.toFloat()
//                    "LAT" -> latitude = entry.value.toFloat()
//                    "SOG" -> speedOverGround = entry.value.toFloat()
//                    "COG" -> courseOverGround = entry.value.toFloat()
//                    "HEADING" -> heading = entry.value.toDouble().toInt()
//                }
//            }
//        }
//    }
//
//    fun exportMessages(username: String, password: String, receiveTime: Int, filename: String) {
//        try {
//            log.info("start exportMessages local messages, host: $host, port: $port, receiveTime: ${receiveTime}s")
//            val model = MinaModel("127.0.0.1", port)
//            val messages = mutableListOf<String>()
//            model.sessionOpened = { it ->
//                it.write(IoBuffer.wrap(MinaUtil.getHexArray(username, password)))
//            }
//            model.messageReceived = { it, _: IoSession ->
//                messages.add("${System.currentTimeMillis()} $it")
//            }
//
//            model.start()
//            Thread.sleep(receiveTime * 1L)
//            model.close()
//
//            FileUtils.writeLines(File(System.getProperty("user.dir") + File.separator + "data" + File.separator + filename), messages)
//        } catch (e: Exception) {
//            errorlog(e)
//        }
//
//    }
//}

class UdpMinaModel: MinaModel {
    private var udpConnector: NioDatagramConnector? = null
    private var udpAcceptor: NioDatagramAcceptor? = null

    constructor()

    constructor(port: Int) {
        this.port = port
        handler = BaseHandler(this)
    }

    constructor(ip: String, port: Int, ifAutoStart: Boolean = false) {
        this.ip = ip
        this.port = port
        handler = BaseHandler(this)
        this.ifAutoStart = ifAutoStart
        autoRestart()
    }

    override fun start(charset: String) {
        if (ifServer) {
            startUdpServer()
        } else {
            startUdpClient(charset)
        }
    }

    private fun startUdpClient(charset: String) {
        try {
            udpConnector = NioDatagramConnector() // 设置过滤器,选用Mina自带的过滤器一行一行读取代码
            udpConnector!!.filterChain.addLast("myChain", ProtocolCodecFilter(TextLineCodecFactory(Charset.forName("UTF-8"), LineDelimiter.CRLF, LineDelimiter.CRLF)))
            udpConnector!!.connectTimeoutMillis = 3 * 1000// 设置连接超时时间
            udpConnector!!.handler = handler
            val future = udpConnector!!.connect(InetSocketAddress(ip, port))//"*************", 4001))//(host, port))
            future.awaitUninterruptibly()
            clientSession = future.session
//            clientSession?.write("Hello from client at ${intToStr((System.currentTimeMillis() / 1000).toInt())}")
        } catch (e: Exception) {
            ifNormal = false
            exceptionMessage = e.message ?: ""
            log.error(e.message)
            connector?.dispose()
            startClientException()
        }
    }

    private fun startUdpServer() {
        try {
            udpAcceptor = NioDatagramAcceptor()
            val lineCodec = TextLineCodecFactory(Charset.forName("UTF-8"), "@", "@")//LineDelimiter.CRLF
            lineCodec.decoderMaxLineLength = 1024 * 10240 //10M
            lineCodec.encoderMaxLineLength = 1024 * 10240
            udpAcceptor!!.filterChain.addLast("myChain", ProtocolCodecFilter(lineCodec))
            udpAcceptor!!.handler = handler
            udpAcceptor!!.bind(InetSocketAddress(port))
        } catch (e: Exception) {
            ifNormal = false
            exceptionMessage = e.message ?: ""
            log.error(e.message)
        }
    }

    override fun restart(message: String) {
        restartInfo.add(dateFormat(Date()) + "," + message)
        if (restartInfo.size > 100) {
            restartInfo.clear()
        }
        restartUdpMinaModel()
    }

    private fun restartUdpMinaModel() {
        closeUdpMinaModel()
        start()
    }

    private fun closeUdpMinaModel() {
        try {
            if (ifServer) {
                acceptor?.unbind()
                acceptor?.dispose()
            } else {
                connector?.dispose()
            }
            TimeUnit.SECONDS.sleep(1)
        } catch (e: Exception) {
            log.error("close ${if(ifServer) "server" else "client"}: ${e.message}")
        }
    }
}
