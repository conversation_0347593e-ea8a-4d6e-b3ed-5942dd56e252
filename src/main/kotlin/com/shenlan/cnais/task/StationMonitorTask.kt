package com.shenlan.cnais.task

import com.shenlan.cnais.auto.StationmonitorMapper
import com.shenlan.cnais.util.dateTimeFormatter
import com.shenlan.cnais.util.getBean
import com.shenlan.cnais.util.isEmpty
import com.shenlan.cnais.util.toLocalDateTime
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
object StationMonitorTask {
    val reportTimeMap = mutableMapOf<String, MutableList<String>>()

    fun init() {
        getBean(StationmonitorMapper::class.java).getAll().forEach {
            reportTimeMap.getOrPut(it.fromType) { mutableListOf() }.add(it.latestReportTime ?: "")
        }
    }

    // 每分钟更新
    @Scheduled(initialDelay = (60 * 1000).toLong(), fixedDelay = (60 * 1000).toLong())
    fun updateReportTime() {
        val mapper = getBean(StationmonitorMapper::class.java)
        reportTimeMap.forEach { (fromType, reportTimeList) ->
            if (reportTimeList.isNotEmpty()) {
                val localDateTimeList = reportTimeList.mapNotNull { it.toLocalDateTime() }
                localDateTimeList.max()?.let { mapper.updateReportTime(fromType, dateTimeFormatter.format(it)) }
            }
        }

        reportTimeMap.clear()
    }
}