package com.shenlan.cnais.util

import org.springframework.scheduling.annotation.Scheduled
import java.io.File
import java.sql.*

class DatabaseConnector {
    var url: String = ""
    var user: String = ""
    var password: String = ""
    var connection: Connection? = null
    var autoQuery = ""
    var autoFunction = { _: ResultSet -> }

    constructor()

    constructor(url: String, user: String, password: String) {
        this.url = url
        this.user = user
        this.password = password
        connect()
    }

    private fun connect() {
        try {
            connection = DriverManager.getConnection(url, user, password)
            log.info("Connected to the database $url successfully")
        } catch (e: Exception) {
            e.printStackTrace()
            log.error("Failed to connect to the database $url")
        }
    }

    // 检查连接是否有效
    private fun isConnectionValid(): Boolean {
        return try {
            connection?.isValid(2) ?: false // 2秒内验证连接有效性
        } catch (e: SQLException) {
            e.printStackTrace()
            log.error("Failed to check the connection validity, the database $url")
            false
        }
    }

    // 执行SQL查询
    fun executeQuery(query: String): ResultSet? {
        try {
            // 如果连接无效，则重新连接
            if (!isConnectionValid()) {
                log.info("The connection $url lost. Reconnecting...")
                connect()
            }

            val statement: Statement? = connection?.createStatement()
            val resultSet = statement?.executeQuery(query)
            return resultSet
        } catch (e: SQLException) {
            e.printStackTrace()
            log.error("An error occurred during query execution, $query")
            return null
        }
    }

    // 关闭连接
    fun close() {
        try {
            connection?.close()
            log.info("Database connection $url closed")
        } catch (e: SQLException) {
            e.printStackTrace()
        }
    }

    fun run() {
        if (autoQuery.isNotEmpty()) {
            executeQuery(autoQuery)?.use { set ->
                autoFunction(set)
            }
        }
    }
}
