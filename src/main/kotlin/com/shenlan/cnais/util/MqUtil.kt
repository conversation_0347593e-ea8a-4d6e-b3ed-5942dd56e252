package com.shenlan.cnais.util

import com.shenlan.cnais.config.AppPro
import org.apache.activemq.ActiveMQConnectionFactory
import org.springframework.jms.core.JmsTemplate
import javax.jms.DeliveryMode
import javax.jms.MessageListener
import javax.jms.Session
import javax.jms.TextMessage

/**
 * Created by Administrator on 2020/1/3.
 */
fun sendTomq(data: String, mqName: String = "app_beidou") {
//    "".log.info("send to mq app_beidou: ${data}")
    if (data.isEmpty()) {
        return
    }
    try {
        getBean(JmsTemplate::class.java).apply {
            this.isExplicitQosEnabled = true
            this.deliveryMode = DeliveryMode.NON_PERSISTENT
        }.convertAndSend(mqName, data)
    } catch (e: Exception) {
        val connectionFactory = ActiveMQConnectionFactory(AppPro.mqUrl)
        val connection = connectionFactory.createConnection()
        connection.start()
        val session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)
        val destination = session.createQueue(mqName)
        val producer = session.createProducer(destination)
        producer.deliveryMode = DeliveryMode.NON_PERSISTENT
        val message = session.createTextMessage(data)
        producer.send(message)
        session.close()
        connection.close()
    }
}

fun createJmsListener(mqname: String) {
    val connectionFactory = ActiveMQConnectionFactory(AppPro.mqUrl)
    val connection = connectionFactory.createConnection()
    connection.start()
    val session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)
    val destination = session.createQueue(mqname)
    val consumer = session.createConsumer(destination)
    consumer.messageListener = MessageListener {
        val text = (it as TextMessage).text
//        MergeDataUtil.dealMqData(mqname, text)
    }
}
////在搭建青岛系统时 青岛的数据先由天津推送 等青岛的灯器指挥好全都改好了 就不用推送
//fun sendToqd(data: String, mqName: String = "tianjin_qingdao") {
//    try {
//        val connectionFactory = ActiveMQConnectionFactory(AppPro.qingdaoMqurl)
//        val connection = connectionFactory.createConnection()
//        connection.start()
//        val session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)
//        val destination = session.createQueue(mqName)
//        val producer = session.createProducer(destination)
//        producer.deliveryMode = DeliveryMode.NON_PERSISTENT
//        val message = session.createTextMessage(data)
//        producer.send(message)
//        session.close()
//        connection.close()
//    } catch (e: Exception) {
//        "".log.error(e.message)
//    }
//}