package com.shenlan.cnais.ais

import com.shenlan.cnais.ais.server.beidou.BDPosition
import com.shenlan.cnais.ais.server.beidou.BDShipData
import com.shenlan.cnais.ais.server.beidou.BDShipInfo
import com.shenlan.cnais.ais.server.oceanboat.UpRealLocation
import com.shenlan.cnais.auto.*
import com.shenlan.cnais.util.log
import dk.dma.ais.message.AisMessage
import dk.dma.ais.sentence.Sentence
import dk.dma.ais.sentence.SentenceLine

/**
 * Created by Administrator on 2022/2/24.
 */

object Aisc { //Ais长用变量
    var TYPE_STATION = "0"
    var TYPE_MERGE = "1"
    var TYPE_PLAT = "2"
    var VDO = "VDO"
    var VDM = "VDM"
    var ALR = "ALR"
    var PSTT = "PSTT"  //萨博私有协议
    var TXT = "TXT"
    var BSCFG = "BSCFG"//对应BCF ACA CAB DLM
    var ADS = "ADS"
    var FSR = "FSR"
    var TSR = "TSR"
    var VER = "VER"
    var VSI = "VSI"
    var crlf = "\r\n"
    var STANDARDHEAD = mutableListOf<String>(VDO, VDM, ALR, TXT, ADS, FSR, TSR, VER, VSI) //根据萨博pstt软件定义 这些头可以在61162和62320找到
    var VDOVDM = mutableListOf<String>(VDO, VDM)//需要解析的报文
    fun ifStandardHead(head: String) = STANDARDHEAD.contains(head)
    fun ifVdoVdm(head: String) = VDOVDM.contains(head)

    var QUEUE_SIZE = 200000
    var MERGE_IFREMOVEREPEAT = true//是否去重
    var MERGE_IFCLEAR = false//是否清洗
    var MERGE_IFSTANDAED = true//是否只输出标准报文

    //1 2 3 4 5 6 7 8 9 :  ;  (   =  )  ?  @  A  B  C  D  E  F  G  H
    //                  10 11 12 13 14 15 16 17 18 19 20 21 22 23 24
    //1-24号报文对应的首字母

}

class Chposition {
    var date: String = ""
    var mmsi = ""
    var code = 0
    var turn = 0
    var speed: Float = 0.0f
    var accuracy = 0
    var geohash = ""
    var lat = 0.0f
    var lng = 0.0f
    var course = 0.0f
    var heading = 0

    var ifClassa = 1
    var stationId = ""
    var fromType = ""

    constructor(date: String, geohash: String, mmsi: String, code: Int, turn: Int, speed: Float, accuracy: Int, lat: Float, lng: Float, course: Float, heading: Int) {
        this.date = date.substring(0, 19)//后面把时间改成增加毫秒 但位置报文不需要
        this.geohash = geohash
        this.mmsi = mmsi
        this.code = code
        this.turn = turn
        this.speed = speed
        this.accuracy = accuracy
        this.lat = lat
        this.lng = lng
        this.course = course
        this.heading = heading
    }
}

class Chdatagram {
    var date: String = ""
    var stationMmsi = ""
    var type = ""
    var hash = 0
    var messageId = ""
    var mmsi = ""
    var message = ""
    var geohash = ""
    var lat = 0.0f
    var lng = 0.0f
    var ifRepeat = 0//0否1是
    var ifClear = 0//0正常报文 1非标准报文 2解析异常 3以后都是数据值域异常
    var mark = ""
    var fromType = ""

    constructor()
    constructor(date: String, stationMmsi: String, type: String, messageId: String, mmsi: String, message: String, hash: Int, ifClear: Int, ifRepeat: Int, mark: String) {
        this.date = date
        this.stationMmsi = stationMmsi
        this.type = type
        this.messageId = messageId
        this.mmsi = mmsi
        this.message = message
        this.ifClear = ifClear
        this.hash = hash
        this.ifClear = ifClear
        this.ifRepeat = ifRepeat
        this.mark = mark
    }

    constructor(date: String, stationMmsi: String, type: String, messageId: String, mmsi: String, message: String, hash: Int, ifClear: Int, ifRepeat: Int, mark: String, fromType: String) {
        this.date = date
        this.stationMmsi = stationMmsi
        this.type = type
        this.messageId = messageId
        this.mmsi = mmsi
        this.message = message
        this.ifClear = ifClear
        this.hash = hash
        this.ifClear = ifClear
        this.ifRepeat = ifRepeat
        this.mark = mark
        this.fromType = fromType
    }

    constructor(date: String, stationMmsi: String, type: String, messageId: String, mmsi: String, message: String, hash: Int, geohash: String, lat: Float, lng: Float) {
        this.date = date
        this.stationMmsi = stationMmsi
        this.type = type
        this.messageId = messageId
        this.mmsi = mmsi
        this.message = message
        this.geohash = geohash
        this.hash = hash
        this.lat = lat
        this.lng = lng
    }
}

//(shipWidth UInt32,positionFixingDeviceCode Int8,eta DATETIME,draught Float32,destination String,dataTerminalReady Int8)ENGINE = MergeTree() PARTITION BY toYYYYMM(date) ORDER BY (date, mmsi);
//class ChShip {
//    var date: String = ""
//    var stationMmsi = ""
//    var type: Int = 0
//    var mmsi: String = ""
//    var imo: String = ""
//    var callSign = ""
//    var shipNameEn = ""
//    var shipName = ""
//    var shipTypeCode = ""
//    var easyTypeCode = ""
//    var shipLength = 0
//    var shipWidth = 0
//    var positionFixingDeviceCode = 0
//    var eta = ""
//    var draught = 0.0
//    var dataTerminalReady = 0
//}

class RawData {
    var stationId = ""
    var sid = ""
    var date = "" //以前是存秒数 导致导出的数据1s内的数据没有排序
    var utc = 0L
    var data = ""//原始报文
    var ifStandardHead = false
    var ifVdmVdo = false
    var commblock = ""
    var head = ""//VDM VSI..
    var message = "" //如果有commblock头的话 content就是去除commblock后的内容 如果没有就和data一样
    var content = "" //如果是vdm vdo的话 就是具体要解析的内容
    var hash = 0 //hash值
    var ifFirst = false //是否报文的第一条
    var currentNum = 0 //当前报文的条数
    var sumNum = 0 //报文的总条数
    var ifrepeat = false //是否重复
    //    open var ifEnd = false//是否是报文的最后一条
    var rawDataList = mutableListOf<RawData>()//第二条 第三条报文
    var vsi: RawData? = null
    var ifVsi = true //新增ifvsi 是为了 是否选择了输出vsi语句
    var ifClear = false //是否清洗
        get() {
            return clearType != 0
        }
    var clearType = 0//0正常报文 1非标准报文 2解析异常 3以后都是数据值域异常
    //自定义标记数据 !SL,id(设备id),clear(清洗类型),time(utc秒数);(辖区id),repeat(是否重复),difftime(相差时间);repeat(是否重复),difftime(相差时间)
    var markData = ""
    var messageId: String = ""

    //存放报文中需要过滤的字段值
    var valueMap = mutableMapOf<String, Any>()
    var alarmCode = ""
    var alarmValue = ""
    var ifAlr = false
    var slot = 0
    var channel = "" //信道

    var vsiTime = ""
    var utcMill = 0L //utc毫秒
    var sentence: SentenceLine = SentenceLine()

    //
    var aisMessage: AisMessage? = null
    var userId=-1

    var ifHiFleet: Boolean = false
    var ifKafka: Boolean = false
    var fromType: String = ""
    var ifHEDESatellite = false
    var columnMap = mutableMapOf<String, String>()
    var upRealLocation: UpRealLocation = UpRealLocation()
    var apiData: Any? = null
    var bdShipData: BDShipData? = null

    constructor(stationmmsi: String, date: String, data: String) {
        this.stationId = stationmmsi
        this.date = date
        this.utcMill = System.currentTimeMillis()
        this.utc = (utcMill / 1000)
        dealMark(data)
        init()
    }

    constructor(data: String) {
//        dealMark(data)
        init()
    }

    constructor(data: String, fromType: String) {
//        dealMark(data)
        this.fromType = fromType
        this.data = data
    }

    fun dealMark(data: String) {
        if (data.contains("##")) {
            var datas = data.split("##")
            markData = datas[1]
            this.data = datas[0]
            this.stationId = datas[1].split(",")[1]//用户接海区的时候没有数据 是因为没有把基站id重新赋值 导致的
        } else {
            this.data = data
        }
    }

    constructor()

    fun getLength(): Int {
        var len = message.length
        if (rawDataList.size > 0) {
            len += rawDataList.sumBy { it.message.length }
        }
        if (vsi != null) {
            len += vsi!!.message.length
        }
        return len
    }

    fun getOutData(ifSystem: Boolean = false): String {//根据不同的选项输出不同的格式
        var rlt = commblock + message
        if (ifSystem) {
            rlt += "##" + markData
        }
        if (rawDataList.size > 0) {
            rlt = rlt + Aisc.crlf + rawDataList.map { commblock + it.message }.joinToString(Aisc.crlf)
        }
        if (ifVsi == true && vsi != null) {
            return rlt + Aisc.crlf + vsi!!.commblock + vsi!!.message
        } else {
            return rlt
        }
    }

    fun getOutDataStore(): String {//存储的时候发现报文里有commblock 是因为如果用户连接的话 上面就会带commblock
        var rlt = message
        if (rawDataList.size > 0) {
            rlt = rlt + Aisc.crlf + rawDataList.map { it.message }.joinToString(Aisc.crlf)
        }
        if (ifVsi == true && vsi != null) {
            return rlt + Aisc.crlf + vsi!!.message
        } else {
            return rlt
        }
    }

    fun init() {
        try {
            sentence.parse(data)
            commblock = ""//sentence.prefix
            head = sentence.formatter ?: ""
            message = sentence.sentence
            ifStandardHead = Aisc.ifStandardHead(head)
            ifVdmVdo = (head == Aisc.VDM || head == Aisc.VDO)
            if (ifVdmVdo) {
                channel = sentence.fields[4]
                content = sentence.fields[5]
                //改一下hash的计算方式
                hash = (content).hashCode()//不能只比内容 vdo vdm的内容可能相同也要保留 不同信道也要保留
                val thisNum = Sentence.parseInt(sentence.fields[2])
                ifFirst = (1 == thisNum)
                currentNum = thisNum
                sumNum = (sentence.fields[1]).toInt()
            } else {
                hash = message.hashCode()
            }
            if (head == Aisc.VSI) {
                sid = sentence.fields[1]
                slot = sentence.fields[4].toInt()
                vsiTime = sentence.fields[3]

            }
        } catch (e: Exception) {
            log.error(e.message + " " + data)
        }
    }

    /**
     *2022-12-08T00:00:44.454Z;!ABVDM,2,1,9,B,56::7E800000<E0B220acVo<<E0B22222222221Jwwwww40Ht0000000,0*5E
    2022-12-08T00:00:44.454Z;!ABVDM,2,2,9,B,000000000000000,2*25
    2022-12-08T00:00:44.454Z;$ABVSI,NH shuiyuancun,9,000044,1658,-106,2*0E
    2022-12-08T00:00:44.628Z;!ABVDM,2,1,9,B,56::7E800000<E0B220acVo<<E0B22222222221Jwwwww40Ht00000000000,0*5E
    2022-12-08T00:00:44.628Z;!ABVDM,2,2,9,B,00000000000,2*25
    2022-12-08T00:00:44.628Z;$ABVSI,SIDAOGOU,9,000044,1658,-97,13*50
    不同基站在相同时隙、相同信道收到的同一条msg5，payload部分拼接起来是一致的，应当视作重复报文。
     */
    fun add(rawData: RawData) {
        rawDataList.add(rawData)
        hash = ( content + rawDataList.map { it.content }.joinToString("")).hashCode()
    }
}


class ChModel {
    var aispositionList = mutableListOf<Chposition>()
    var aispositionList2 = mutableListOf<Aisposition>()
    var aispositionhedeList = mutableListOf<Aispositionhede>()
    var aisdatagramList = mutableListOf<Chdatagram>()
    var aisvirtualatonList = mutableListOf<Aisvirtualaton>()
    var aisstationList = mutableListOf<Aisstation>()
    var aisshipList = mutableListOf<Aisship>()
    var alarmList = mutableListOf<Triple<String, String, String>>()//基站id alarmcode alarmvalue
    var psttMap = mutableMapOf<String, MutableList<String>>()//基站设备pstt语句 和fsr
    fun loginfo() {
        log.info("position:${aispositionList.size} datagram:${aisdatagramList.size} aton:${aisvirtualatonList.size} station:${aisstationList.size} ship:${aisshipList.size}")
    }

    var ifInsertCh: Int = 0//0不更新数据库和ch  1更新
    var bdShipInfoList: MutableList<BDShipInfo> = mutableListOf()
    var bdPositionList: MutableList<BDPosition> = mutableListOf()
}


